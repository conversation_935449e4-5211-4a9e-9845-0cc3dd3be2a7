-- 技能删除模块
-- 根据放弃技能列表批量删除技能
-- 作者：Cfan
-- 版本：1.0

--[[
使用说明：
1. 设置放弃技能列表：var["fangqi_skills_list"] = {"force", "trade", "dodge"}
2. 或者从文件读取：load_fangqi_skills_from_file("skills.txt")
3. 执行删除：exec("delete_skills_batch")
4. 单个删除：exec("delete_single_skill force 1")

技能列表格式：
- 简单格式：{"force", "trade", "dodge"} - 默认删除1级
- 详细格式：{{"force", 1}, {"trade", 0}, {"dodge", 2}} - 指定删除级数，0表示全部删除
]]

-- 全局变量初始化
var["fangqi_skills_list"] = var["fangqi_skills_list"] or {}
var["delete_skills_log"] = var["delete_skills_log"] or {}

-- 当前删除状态
local current_skill_index = 1
local is_deleting = false
local delete_timer_name = "delete_skills_timer"

-- 技能删除确认触发器
add_trigger("delete_skills_confirm", "^[ > ]*你确定要放弃.*技能吗？", function(params)
    log_message("确认放弃技能", C.y, "<技能删除>")
    send("y")
end)

-- 技能删除成功触发器
add_trigger("delete_skills_success", "^[ > ]*你放弃了.*技能", function(params)
    local skill_name = var["current_deleting_skill"] or "未知技能"
    log_message("成功放弃技能：" .. skill_name, C.g, "<技能删除>")
    
    -- 记录到删除日志
    table.insert(var["delete_skills_log"], {
        skill = skill_name,
        time = os.date("%Y-%m-%d %H:%M:%S"),
        status = "成功"
    })
    
    -- 继续删除下一个技能
    continue_delete_next_skill()
end)

-- 技能删除失败触发器
add_trigger("delete_skills_failed", "^[ > ]*(?:你没有这个技能|你的.*技能等级不够|无法放弃)", function(params)
    local skill_name = var["current_deleting_skill"] or "未知技能"
    log_message("放弃技能失败：" .. skill_name .. " - " .. params[-1], C.r, "<技能删除>")
    
    -- 记录到删除日志
    table.insert(var["delete_skills_log"], {
        skill = skill_name,
        time = os.date("%Y-%m-%d %H:%M:%S"),
        status = "失败：" .. params[-1]
    })
    
    -- 继续删除下一个技能
    continue_delete_next_skill()
end)

-- 从文件加载放弃技能列表
function load_fangqi_skills_from_file(filename)
    local skills_list = {}
    local file_path = filename or "fangqi_skills.txt"
    
    log_message("尝试从文件加载技能列表：" .. file_path, C.w, "<技能删除>")
    
    -- 这里可以根据实际需要实现文件读取逻辑
    -- 由于ZMUD环境限制，这里提供一个示例格式
    
    -- 示例：如果文件不存在，使用默认列表
    if not skills_list or #skills_list == 0 then
        log_message("文件不存在或为空，使用默认技能列表", C.y, "<技能删除>")
        skills_list = {
            {"force", 1},      -- 放弃内功1级
            {"trade", 0},      -- 放弃所有贸易技能
            {"medicine", 2}    -- 放弃医术2级
        }
    end
    
    var["fangqi_skills_list"] = skills_list
    log_message("加载技能列表完成，共" .. #skills_list .. "个技能", C.g, "<技能删除>")
    return skills_list
end

-- 设置放弃技能列表（从变量）
function set_fangqi_skills_list(skills_list)
    if not skills_list or type(skills_list) ~= "table" then
        log_message("技能列表格式错误", C.r, "<技能删除>")
        return false
    end
    
    var["fangqi_skills_list"] = skills_list
    log_message("设置技能列表完成，共" .. #skills_list .. "个技能", C.g, "<技能删除>")
    return true
end

-- 继续删除下一个技能
function continue_delete_next_skill()
    unset_timer(delete_timer_name)
    
    local skills_list = var["fangqi_skills_list"] or {}
    
    if current_skill_index > #skills_list then
        -- 所有技能删除完成
        finish_delete_skills()
        return
    end
    
    -- 延迟执行下一个删除，避免命令过快
    set_timer(delete_timer_name, 2, function()
        delete_next_skill()
    end)
end

-- 删除下一个技能
function delete_next_skill()
    local skills_list = var["fangqi_skills_list"] or {}
    
    if current_skill_index > #skills_list then
        finish_delete_skills()
        return
    end
    
    local skill_info = skills_list[current_skill_index]
    local skill_name, skill_level
    
    -- 解析技能信息
    if type(skill_info) == "string" then
        skill_name = skill_info
        skill_level = 1  -- 默认删除1级
    elseif type(skill_info) == "table" then
        skill_name = skill_info[1]
        skill_level = skill_info[2] or 1
    else
        log_message("技能信息格式错误：" .. tostring(skill_info), C.r, "<技能删除>")
        current_skill_index = current_skill_index + 1
        continue_delete_next_skill()
        return
    end
    
    -- 记录当前删除的技能
    var["current_deleting_skill"] = skill_name
    
    -- 执行删除命令
    delete_single_skill_internal(skill_name, skill_level)
    
    current_skill_index = current_skill_index + 1
end

-- 内部单个技能删除函数
function delete_single_skill_internal(skill_name, skill_level)
    if not skill_name or skill_name == "" then
        log_message("技能名称不能为空", C.r, "<技能删除>")
        continue_delete_next_skill()
        return
    end
    
    skill_level = skill_level or 1
    
    local command
    if skill_level == 0 then
        -- 删除所有等级
        command = "fangqi " .. skill_name
        log_message("开始放弃技能：" .. skill_name .. "（全部等级）", C.w, "<技能删除>")
    else
        -- 删除指定等级
        command = "fangqi " .. skill_name .. " " .. skill_level
        log_message("开始放弃技能：" .. skill_name .. "（" .. skill_level .. "级）", C.w, "<技能删除>")
    end
    
    -- 使用check_busy确保命令执行时机正确
    check_busy(function()
        send(command)
    end)
end

-- 完成技能删除
function finish_delete_skills()
    is_deleting = false
    current_skill_index = 1
    var["current_deleting_skill"] = nil
    unset_timer(delete_timer_name)
    
    local total_count = #(var["delete_skills_log"] or {})
    local success_count = 0
    local failed_count = 0
    
    -- 统计删除结果
    for _, log_entry in ipairs(var["delete_skills_log"] or {}) do
        if log_entry.status == "成功" then
            success_count = success_count + 1
        else
            failed_count = failed_count + 1
        end
    end
    
    log_message("技能删除完成！总计：" .. total_count .. "，成功：" .. success_count .. "，失败：" .. failed_count, C.g, "<技能删除>")
    
    -- 显示详细日志
    show_delete_skills_log()
end

-- 显示删除日志
function show_delete_skills_log()
    local logs = var["delete_skills_log"] or {}
    if #logs == 0 then
        echo("\n" .. C.w .. "没有删除记录")
        return
    end
    
    echo("\n" .. C.c .. "=== 技能删除日志 ===")
    for i, log_entry in ipairs(logs) do
        local color = log_entry.status == "成功" and C.g or C.r
        echo("\n" .. color .. log_entry.time .. " - " .. log_entry.skill .. " - " .. log_entry.status)
    end
    echo("\n" .. C.c .. "==================")
end

-- 清空删除日志
function clear_delete_skills_log()
    var["delete_skills_log"] = {}
    log_message("删除日志已清空", C.y, "<技能删除>")
end

-- 批量删除技能（主函数）
add_alias("delete_skills_batch", function(params)
    if is_deleting then
        log_message("技能删除正在进行中，请稍候", C.y, "<技能删除>")
        return
    end
    
    local skills_list = var["fangqi_skills_list"] or {}
    if #skills_list == 0 then
        log_message("放弃技能列表为空，请先设置技能列表", C.r, "<技能删除>")
        echo("\n" .. C.w .. "使用方法：")
        echo("\n" .. C.w .. "1. 设置列表：var[\"fangqi_skills_list\"] = {\"force\", \"trade\"}")
        echo("\n" .. C.w .. "2. 或加载文件：exec(\"load_fangqi_skills_from_file\")")
        echo("\n" .. C.w .. "3. 然后执行：exec(\"delete_skills_batch\")")
        return
    end
    
    -- 重置状态
    is_deleting = true
    current_skill_index = 1
    var["delete_skills_log"] = {}
    
    log_message("开始批量删除技能，共" .. #skills_list .. "个技能", C.g, "<技能删除>")
    
    -- 开始删除第一个技能
    delete_next_skill()
end)

-- 单个技能删除
add_alias("delete_single_skill", function(params)
    local skill_name = params[1]
    local skill_level = tonumber(params[2]) or 1
    
    if not skill_name or skill_name == "" then
        log_message("请指定技能名称", C.r, "<技能删除>")
        echo("\n" .. C.w .. "使用方法：exec(\"delete_single_skill 技能名 等级\")")
        echo("\n" .. C.w .. "例如：exec(\"delete_single_skill force 1\")")
        return
    end
    
    if is_deleting then
        log_message("批量删除正在进行中，无法执行单个删除", C.y, "<技能删除>")
        return
    end
    
    -- 清空日志并记录单个删除
    var["delete_skills_log"] = {}
    var["current_deleting_skill"] = skill_name
    
    delete_single_skill_internal(skill_name, skill_level)
end)

-- 停止技能删除
add_alias("stop_delete_skills", function(params)
    if not is_deleting then
        log_message("当前没有进行技能删除", C.w, "<技能删除>")
        return
    end
    
    is_deleting = false
    current_skill_index = 1
    var["current_deleting_skill"] = nil
    unset_timer(delete_timer_name)
    
    log_message("技能删除已停止", C.y, "<技能删除>")
end)

-- 显示当前技能列表
add_alias("show_fangqi_skills", function(params)
    local skills_list = var["fangqi_skills_list"] or {}
    
    if #skills_list == 0 then
        echo("\n" .. C.w .. "放弃技能列表为空")
        return
    end
    
    echo("\n" .. C.c .. "=== 放弃技能列表 ===")
    for i, skill_info in ipairs(skills_list) do
        local skill_name, skill_level
        if type(skill_info) == "string" then
            skill_name = skill_info
            skill_level = 1
        elseif type(skill_info) == "table" then
            skill_name = skill_info[1]
            skill_level = skill_info[2] or 1
        end
        
        local level_text = skill_level == 0 and "全部等级" or (skill_level .. "级")
        echo("\n" .. C.w .. i .. ". " .. skill_name .. " (" .. level_text .. ")")
    end
    echo("\n" .. C.c .. "==================")
end)

-- 关闭所有相关触发器
function close_delete_skills()
    del_trigger("delete_skills_confirm")
    del_trigger("delete_skills_success")
    del_trigger("delete_skills_failed")
    unset_timer(delete_timer_name)
    is_deleting = false
    current_skill_index = 1
    var["current_deleting_skill"] = nil
end

-- 初始化时关闭可能存在的触发器
close_delete_skills()

log_message("技能删除模块加载完成", C.g, "<系统>")
