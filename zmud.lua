require "trigger"                   -- 自定义触发器
require "alias"                     -- 自定义alias解析器
require "function"                  -- 通用函数
require "var"                       -- 自定义的变量var
--------------------------------------
--------------------------------------
require "place"                     -- 房间抓取触发
require "timer"                     -- 自定义的定时器
require "copytable"                 -- table表
require "cmud"                      -- 照搬cmud算路径
require "rooms"                     -- 所有房间信息--地图数据库
require "dazuo"                     -- 打坐模块
require "walk"                      -- 行走模块
require "gps"                       -- gps定位
require "dujiang"                   -- 渡江
require "boat"                      -- 坐船
require "maze"                      -- 迷宫，特殊路线
require "guard"                     -- 清除地图拦路守卫
require "trade"                     -- 买，卖，取钱存钱
require "common"                    -- 一般触发
----------------------------------------
require "fight"                     -- 战斗
require "job"                       -- job模块
--[[
具体任务采用缓加载策略，用到哪个模块，就加载哪个模块
--]]
require "story"                     -- 自动解密
require "fullskills"                -- 百强fullskill
----------------------------------------
require "xuexi"                     -- 学习
require "lingwu_lianxi"             -- 领悟练习
require "log"                       -- log
require "bigwords"                  -- 大字识别
require "Instance.ttt"              -- 通天塔
require "Instance.challenge"        -- 宗师
require "Instance.qinghuaimeng"     -- 情怀梦
require "Instance.haizhan"          -- 海战
require "quest.emjy"                -- 峨眉九阴
require "quest.kanglong"            -- 亢龙
require "jueji"                     -- 绝技
----------------------------------------
require "prepare"                   -- 任务准备
require "quit"                      -- 退出
require "quest"                     -- 小quest
require "heal"                      -- 疗伤
require "repair"                    -- 维修武器防具
require "status"                    -- 状态
require "import_settings"           -- 导入设置
require "help.help_newbie"          -- 新手帮助
require "ui"                        -- UI界面函数
require "fangqi_skills"