-- 技能删除模块
-- 根据放弃技能列表自动删除已拥有的技能
-- 作者：Cfan
-- 版本：2.0

--[[
使用说明：
1. 设置放弃技能列表：var["fangqi_skills_list"] = "yuxiao-jian|hamagong|force"
2. 执行检查并删除：exec("check_and_delete_skills")
3. 查看当前拥有的技能：exec("show_my_skills")

功能说明：
- 自动检查当前拥有的技能（从var["skills_level"]读取）
- 如果技能在放弃列表中，就自动放弃该技能
- 支持用"|"分隔的技能列表格式
]]

-- 全局变量初始化
var["fangqi_skills_list"] = var["fangqi_skills_list"] or ""
var["delete_skills_log"] = var["delete_skills_log"] or {}

-- 当前删除状态
local current_skill_index = 1
local is_deleting = false
local delete_timer_name = "delete_skills_timer"
local skills_to_delete = {}  -- 需要删除的技能列表

-- 技能删除确认触发器
add_trigger("delete_skills_confirm", "^[ > ]*你确定要放弃.*技能吗？", function(params)
    log_message("确认放弃技能", C.y, "<技能删除>")
    send("y")
end)

-- 技能删除成功触发器
add_trigger("delete_skills_success", "^[ > ]*你放弃了.*技能", function(params)
    local skill_name = var["current_deleting_skill"] or "未知技能"
    log_message("成功放弃技能：" .. skill_name, C.g, "<技能删除>")

    -- 记录到删除日志
    table.insert(var["delete_skills_log"], {
        skill = skill_name,
        time = os.date("%Y-%m-%d %H:%M:%S"),
        status = "成功"
    })

    -- 继续删除下一个技能
    continue_delete_next_skill()
end)

-- 技能删除失败触发器
add_trigger("delete_skills_failed", "^[ > ]*(?:你没有这个技能|你的.*技能等级不够|无法放弃)", function(params)
    local skill_name = var["current_deleting_skill"] or "未知技能"
    log_message("放弃技能失败：" .. skill_name .. " - " .. params[-1], C.r, "<技能删除>")

    -- 记录到删除日志
    table.insert(var["delete_skills_log"], {
        skill = skill_name,
        time = os.date("%Y-%m-%d %H:%M:%S"),
        status = "失败：" .. params[-1]
    })

    -- 继续删除下一个技能
    continue_delete_next_skill()
end)

-- 解析放弃技能列表（用"|"分隔）
function parse_fangqi_skills_list(skills_string)
    if not skills_string or skills_string == "" then
        return {}
    end

    local skills = {}
    -- 用"|"分割字符串
    for skill in string.gmatch(skills_string, "([^|]+)") do
        -- 去除前后空格
        skill = string.gsub(skill, "^%s*(.-)%s*$", "%1")
        if skill ~= "" then
            table.insert(skills, skill)
        end
    end

    return skills
end

-- 检查当前拥有的技能并找出需要删除的
function check_skills_to_delete()
    local fangqi_list = parse_fangqi_skills_list(var["fangqi_skills_list"])
    local current_skills = var["skills_level"] or {}
    local to_delete = {}

    if #fangqi_list == 0 then
        log_message("放弃技能列表为空", C.y, "<技能删除>")
        return {}
    end

    log_message("检查需要放弃的技能...", C.w, "<技能删除>")

    -- 检查每个要放弃的技能是否在当前技能中
    for _, skill_name in ipairs(fangqi_list) do
        if current_skills[skill_name] and current_skills[skill_name] > 0 then
            table.insert(to_delete, skill_name)
            log_message("发现需要放弃的技能：" .. skill_name .. " (等级:" .. current_skills[skill_name] .. ")", C.r, "<技能删除>")
        else
            log_message("技能 " .. skill_name .. " 未拥有，跳过", C.w, "<技能删除>")
        end
    end

    return to_delete
end

-- 继续删除下一个技能
function continue_delete_next_skill()
    unset_timer(delete_timer_name)

    if current_skill_index > #skills_to_delete then
        -- 所有技能删除完成
        finish_delete_skills()
        return
    end

    -- 延迟执行下一个删除，避免命令过快
    set_timer(delete_timer_name, 2, function()
        delete_next_skill()
    end)
end

-- 删除下一个技能
function delete_next_skill()
    if current_skill_index > #skills_to_delete then
        finish_delete_skills()
        return
    end

    local skill_name = skills_to_delete[current_skill_index]

    -- 记录当前删除的技能
    var["current_deleting_skill"] = skill_name

    -- 执行删除命令
    delete_single_skill_internal(skill_name)

    current_skill_index = current_skill_index + 1
end

-- 内部单个技能删除函数
function delete_single_skill_internal(skill_name)
    if not skill_name or skill_name == "" then
        log_message("技能名称不能为空", C.r, "<技能删除>")
        continue_delete_next_skill()
        return
    end

    -- 直接放弃技能，不指定等级（放弃所有等级）
    local command = "fangqi " .. skill_name
    log_message("开始放弃技能：" .. skill_name, C.w, "<技能删除>")

    -- 使用check_busy确保命令执行时机正确
    check_busy(function()
        send(command)
    end)
end

-- 完成技能删除
function finish_delete_skills()
    is_deleting = false
    current_skill_index = 1
    var["current_deleting_skill"] = nil
    unset_timer(delete_timer_name)
    
    local total_count = #(var["delete_skills_log"] or {})
    local success_count = 0
    local failed_count = 0
    
    -- 统计删除结果
    for _, log_entry in ipairs(var["delete_skills_log"] or {}) do
        if log_entry.status == "成功" then
            success_count = success_count + 1
        else
            failed_count = failed_count + 1
        end
    end
    
    log_message("技能删除完成！总计：" .. total_count .. "，成功：" .. success_count .. "，失败：" .. failed_count, C.g, "<技能删除>")
    
    -- 显示详细日志
    show_delete_skills_log()
end

-- 显示删除日志
function show_delete_skills_log()
    local logs = var["delete_skills_log"] or {}
    if #logs == 0 then
        echo("\n" .. C.w .. "没有删除记录")
        return
    end
    
    echo("\n" .. C.c .. "=== 技能删除日志 ===")
    for i, log_entry in ipairs(logs) do
        local color = log_entry.status == "成功" and C.g or C.r
        echo("\n" .. color .. log_entry.time .. " - " .. log_entry.skill .. " - " .. log_entry.status)
    end
    echo("\n" .. C.c .. "==================")
end

-- 清空删除日志
function clear_delete_skills_log()
    var["delete_skills_log"] = {}
    log_message("删除日志已清空", C.y, "<技能删除>")
end

-- 检查并删除技能（主函数）
add_alias("check_and_delete_skills", function(params)
    if is_deleting then
        log_message("技能删除正在进行中，请稍候", C.y, "<技能删除>")
        return
    end

    if not var["fangqi_skills_list"] or var["fangqi_skills_list"] == "" then
        log_message("放弃技能列表为空，请先设置技能列表", C.r, "<技能删除>")
        echo("\n" .. C.w .. "使用方法：")
        echo("\n" .. C.w .. "1. 设置列表：var[\"fangqi_skills_list\"] = \"yuxiao-jian|hamagong|force\"")
        echo("\n" .. C.w .. "2. 然后执行：exec(\"check_and_delete_skills\")")
        return
    end

    -- 检查需要删除的技能
    skills_to_delete = check_skills_to_delete()

    if #skills_to_delete == 0 then
        log_message("没有需要放弃的技能", C.g, "<技能删除>")
        return
    end

    -- 重置状态
    is_deleting = true
    current_skill_index = 1
    var["delete_skills_log"] = {}

    log_message("开始删除技能，共" .. #skills_to_delete .. "个技能", C.g, "<技能删除>")

    -- 开始删除第一个技能
    delete_next_skill()
end)

-- 单个技能删除
add_alias("delete_single_skill", function(params)
    local skill_name = params[1]

    if not skill_name or skill_name == "" then
        log_message("请指定技能名称", C.r, "<技能删除>")
        echo("\n" .. C.w .. "使用方法：exec(\"delete_single_skill 技能名\")")
        echo("\n" .. C.w .. "例如：exec(\"delete_single_skill force\")")
        return
    end

    if is_deleting then
        log_message("批量删除正在进行中，无法执行单个删除", C.y, "<技能删除>")
        return
    end

    -- 清空日志并记录单个删除
    var["delete_skills_log"] = {}
    var["current_deleting_skill"] = skill_name

    delete_single_skill_internal(skill_name)
end)

-- 停止技能删除
add_alias("stop_delete_skills", function()
    if not is_deleting then
        log_message("当前没有进行技能删除", C.w, "<技能删除>")
        return
    end

    is_deleting = false
    current_skill_index = 1
    var["current_deleting_skill"] = nil
    unset_timer(delete_timer_name)

    log_message("技能删除已停止", C.y, "<技能删除>")
end)

-- 显示当前技能列表
add_alias("show_fangqi_skills", function()
    local skills_string = var["fangqi_skills_list"] or ""

    if skills_string == "" then
        echo("\n" .. C.w .. "放弃技能列表为空")
        return
    end

    local skills_list = parse_fangqi_skills_list(skills_string)

    echo("\n" .. C.c .. "=== 放弃技能列表 ===")
    for i, skill_name in ipairs(skills_list) do
        echo("\n" .. C.w .. i .. ". " .. skill_name)
    end
    echo("\n" .. C.c .. "==================")
end)

-- 显示当前拥有的技能
add_alias("show_my_skills", function()
    local current_skills = var["skills_level"] or {}

    if not next(current_skills) then
        echo("\n" .. C.w .. "当前没有技能信息")
        return
    end

    echo("\n" .. C.c .. "=== 当前拥有的技能 ===")
    for skill_name, skill_level in pairs(current_skills) do
        if skill_level > 0 then
            echo("\n" .. C.w .. skill_name .. " - " .. skill_level .. "级")
        end
    end
    echo("\n" .. C.c .. "=====================")
end)

-- 关闭所有相关触发器
function close_delete_skills()
    del_trigger("delete_skills_confirm")
    del_trigger("delete_skills_success")
    del_trigger("delete_skills_failed")
    unset_timer(delete_timer_name)
    is_deleting = false
    current_skill_index = 1
    var["current_deleting_skill"] = nil
end

-- 初始化时关闭可能存在的触发器
close_delete_skills()

log_message("技能删除模块加载完成", C.g, "<系统>")
