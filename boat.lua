-- boat.lua - 船只相关触发器管理模块
-- 负责处理游戏中乘坐船只时的各种事件触发，包括登船、抵达、离开等状态的清理和管理。
-- 作者: Cline
-- 修订日期: 2025-07-03

-- 定义一个辅助函数，用于封装船只相关触发器的通用清理逻辑
-- 当玩家离开船只或进行特定操作时，需要清除相关计时器、打坐状态和变量。
-- 该函数旨在减少代码重复，提高可维护性。
function boat_common_cleanup()
    -- 取消名为"timer"的计时器，防止不必要的延迟或操作
    unset_timer("timer")
    -- 删除名为"check"的计时器，通常用于周期性检查
    del_timer("check")
    -- 将打坐状态设置为"none"，确保角色不在打坐状态
    set_dazuo("none")
    -- 将全局变量var["idle"]设置为0，可能表示角色不再处于空闲状态
    var["idle"] = 0
    -- 将全局变量var["do_stop"]设置为0，可能表示停止当前正在进行的操作
    var["do_stop"] = 0
end

-- boat_1 触发器: 阻止战斗或打坐区域的清理
-- 当触发条件为“这里不准战斗，也不准打坐。”时，执行清理操作。
add_trigger("boat_1", "^\\s*这里不准战斗，也不准打坐。", function(params)
    -- 调用通用清理函数
    boat_common_cleanup()
end)

-- boat_2 触发器: 抵达小岛或岸边
-- 当触发条件为“终于到了小岛边，船夫把小舟靠在岸边，快下船吧。”或“终于到了岸边，船夫把小舟靠在岸边，快下船吧。”时，
-- 执行清理操作，关闭所有boat系列触发器，并发送"out"命令尝试离开。
add_trigger("boat_2", "^\\s*(?:终于到了小岛边，||终于到了岸边，)船夫把小舟靠在岸边，快下船吧。", function(params)
    -- 调用通用清理函数
    boat_common_cleanup()
    -- 关闭所有与船只相关的触发器，避免重复触发或干扰后续操作
    close_triggers("boat", 1, 4)
    -- 发送"out"命令，尝试离开船只或当前区域
    send("out")
    -- 保持行走状态，可能用于自动寻路或离开当前场景
    keepwalk()
end)

-- boat_3 触发器: 艄公提示下船
-- 当触发条件为“艄公轻声说道：“都下船吧，我也要回去了。””或“艄公说“到啦，上岸吧”，随即把一块踏脚板搭上堤岸。”时，
-- 执行清理操作，关闭所有boat系列触发器，并发送"out"命令尝试离开。
add_trigger("boat_3", "^\\s*艄公(?:轻声说道：“都下船吧，我也要回去了。”|说“到啦，上岸吧”，随即把一块踏脚板搭上堤岸。)", function(params)
    -- 调用通用清理函数
    boat_common_cleanup()
    -- 关闭所有与船只相关的触发器
    close_triggers("boat", 1, 4)
    -- 发送"out"命令，尝试离开船只或当前区域
    send("out")
    -- 保持行走状态
    keepwalk()
end)

-- boat_4 触发器: 渡船离开
-- 当触发条件为“渡船 - out”时，执行清理操作，并检查繁忙状态后尝试装备武器。
add_trigger("boat_4", "^\\s*渡船 \\- out", function(params)
    -- 调用通用清理函数
    boat_common_cleanup()
    -- 检查当前是否繁忙，不繁忙则执行回调函数
    check_busy2(function()
        -- 执行装备武器命令，可能是在离开船只后准备进入战斗或进行其他操作
        exec("wield_weapon")
    end)
end)

-- 模块初始化：关闭所有boat系列触发器
-- 在文件加载时，确保所有与船只相关的触发器都处于关闭状态，
-- 以便在需要时通过特定逻辑重新开启。
close_triggers("boat", 1, 4)
