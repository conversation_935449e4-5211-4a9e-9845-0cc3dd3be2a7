-- 简单技能放弃脚本
-- 根据设置的技能列表自动放弃技能

-- 设置要放弃的技能列表，用"|"分隔
-- 例如：var["fangqi_skills_list"] = "yuxiao-jian|hamagong|force"
var["fangqi_skills_list"] = var["fangqi_skills_list"] or ""

local fangqi_list = {}
local current_index = 1

-- 放弃技能确认
add_trigger("fangqi_confirm", "^[ > ]*你确定要放弃.*技能吗？", function(params)
    send("y")
end)

-- 解析放弃技能列表
function parse_fangqi_list()
    fangqi_list = {}
    if var["fangqi_skills_list"] and var["fangqi_skills_list"] ~= "" then
        for skill in string.gmatch(var["fangqi_skills_list"], "([^|]+)") do
            skill = string.gsub(skill, "^%s*(.-)%s*$", "%1")  -- 去除空格
            if skill ~= "" then
                table.insert(fangqi_list, skill)
            end
        end
    end
end

-- 检查并删除技能
function check_and_delete()
    parse_fangqi_list()

    if #fangqi_list == 0 then
        echo("\n" .. C.r .. "放弃技能列表为空！")
        return
    end

    echo("\n" .. C.W .. "开始检查技能...")

    -- 使用现有的技能数据
    local skills_name = var["skills_name"] or {}
    local skills_id = var["skills_id"] or {}

    -- 找出需要删除的技能
    local to_delete = {}
    for _, skill in ipairs(fangqi_list) do
        -- 检查技能是否存在（可能在skills_name或skills_id中）
        local skill_exists = false

        -- 检查skills_id中是否有这个技能
        if skills_id[skill] then
            skill_exists = true
        end

        -- 检查skills_name中是否有这个技能（可能是反向存储）
        for k, v in pairs(skills_name) do
            if v == skill or k == skill then
                skill_exists = true
                break
            end
        end

        if skill_exists then
            table.insert(to_delete, skill)
            echo("\n" .. C.r .. "发现需要放弃的技能：" .. skill)
        else
            echo("\n" .. C.W .. "技能 " .. skill .. " 未拥有，跳过")
        end
    end

    if #to_delete == 0 then
        echo("\n" .. C.g .. "没有需要放弃的技能")
        return
    end

    -- 只放弃第一个技能
    local skill = to_delete[1]
    echo("\n" .. C.y .. "正在放弃技能：" .. skill)
    send("fangqi " .. skill)
end

-- 开始检查技能
add_alias("fangqi_skills", function()
    if not var["fangqi_skills_list"] or var["fangqi_skills_list"] == "" then
        echo("\n" .. C.r .. "请先设置放弃技能列表！")
        echo("\n" .. C.W .. "例如：var[\"fangqi_skills_list\"] = \"yuxiao-jian|hamagong\"")
        return
    end

    -- 直接检查并删除，不需要抓取
    check_and_delete()
end)