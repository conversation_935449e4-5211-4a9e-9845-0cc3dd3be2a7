-- GPS定位系统主函数
add_alias("gps", function(params)
	local do_stop = var["do_stop"] or 0
	if do_stop == 0 then
		open_trigger("gps_1")
		var["roomzone"] = nil
		var["not_wrong_way"] = nil
		local ado = var["ado"] or 0

		-- 根据ado模式执行不同的命令序列
		if ado == 1 and 1 == 0 then
			exes("ado time -s|set look|look|alias action GPS routing...", 5)
		else
			exes("time -s;set look;look;alias action GPS routing...", 5)
		end
	end
end)

-- GPS辅助函数（预留）
add_alias("do_gps", function()
	-- 预留功能
end)

-- GPS定位成功触发器
add_trigger("gps_1", '^[ > ]*你把 \\"action\\" 设定为 \\"GPS routing\\.\\.\\." 成功完成。', function(params)
	close_trigger("gps_1")
	del_timer("input")

	-- 执行GPS定位
	var["gps"] = gps(var["roomzone"], var["roomname"], var["roomdesc"], var["roomnearby"], var["roomexit"],
		var["roomdesc2"])

	if var["gps"] == "" then
		-- GPS定位失败处理
		var["location"] = 0
		if var["debug"] and var["debug"] > 2 then
			echo("\nGPS定位：房间号【0】房间名称：【" .. var["roomname"] .. "】失败！")
		end

		local do_stop = var["do_stop"] or 0
		if do_stop == 0 then
			wait1(6, function()
				exec("gps_fail")
			end)
		end
	else
		-- GPS定位成功处理
		var["location"] = var["gps"]
		var["wrong_way"] = 0
		if var["debug"] and var["debug"] > 2 then
			echo("\nGPS定位：房间号【" .. var["gps"] .. "】房间名称：【" .. var["roomname"] .. "】成功！")
		end
		echo("\n" .. C.W .. var["roomname"] .. ": " .. var["location"])

		local do_stop = var["do_stop"] or 0
		if do_stop == 0 then
			wait1(6, function()
				exec("gps_ok")
			end)
		end
	end
end)


-- 处理武当丛林的GPS失败情况
local function handle_wudang_conglin(_room)
	local check_conglin_1, check_conglin_2, check_conglin_3, check_conglin_4 = {}, {}, {}, {}

	-- 分类丛林类型
	for k, v in ipairs(var["wudangconglin"]) do
		if string.find(v, "丛林边缘") then
			table.insert(check_conglin_1, var["wudangconglinport"][k])
		end
		if string.find(v, "阔叶丛林") then
			table.insert(check_conglin_2, var["wudangconglinport"][k])
		end
		if string.find(v, "积雪丛林") then
			table.insert(check_conglin_3, var["wudangconglinport"][k])
		end
		if string.find(v, "落叶丛林") then
			table.insert(check_conglin_4, var["wudangconglinport"][k])
		end
	end

	-- 按优先级选择路径
	local conglin_types = {
		{ check_conglin_1, "丛林边缘" },
		{ check_conglin_2, "阔叶丛林" },
		{ check_conglin_3, "积雪丛林" },
		{ check_conglin_4, "落叶丛林" }
	}

	for _, conglin_data in ipairs(conglin_types) do
		local conglin_list = conglin_data[1]
		if is_table_empty(conglin_list) == false then
			local _run = conglin_list[math.random(#conglin_list)]
			check_busy(function()
				send(_run)
				exec("gps")
			end)
			return true
		end
	end

	-- 如果所有丛林都没有路径，设置默认位置
	var["location"] = 3512
	var["wrong_way"] = 0
	exec("gps_ok")
	return true
end

-- GPS定位失败处理函数
add_alias("gps_fail", function(params)
	local _room = var["roomname"] or "none"
	local _zone = var["roomzone"] or "none"

	-- 处理武当丛林
	if string.find("落叶丛林|积雪丛林|阔叶丛林", _room) then
		handle_wudang_conglin(_room)
		return
	end

	-- 处理其他特殊房间
	if string.find("武馆", _zone) and not string.find("武馆大门", _room) then
		-- 武馆里面，经验不足时进入武馆
		if var["exp"] and var["exp"] < 3000 then
			require("wuguan")
			exec("wg")
		end
	elseif string.find("烈火丛林", _room) then
		-- 烈火丛林直接设置位置
		var["location"] = 3512
		var["gps"] = 3512
		var["wrong_way"] = 0
		exec("gps_ok")
	elseif string.find("石梁", _room) and _room == "石梁" then
		exec("crossyd12") --离开石梁吧
	elseif string.find("牢房", _room) then
		send("push flag")
		exec("climb up;s;gps")
	elseif string.find("陷阱", _room) then
		send("push flag")
		send("climb up")
		exec("gps")
	elseif _room == "崖壁" then
		check_busy(function(params)
			exec("pa up;gps")
		end)
	elseif _room == "谷底水潭" or _room == "水底通道" then
		check_busy(function(params)
			exec("drop stone;drop stone;qian up;pa up;gps")
		end)
	elseif _room == "水潭表面" then
		exec("drop stone;drop stone;qian up;pa up;gps")
	elseif string.find("瀑布中", _room) then
		send("tiao anbian")
		--	send("jump back")
		exec("gps")
	elseif string.find("九宫桃花阵", _room) then
		migong_rooms["归云庄九宫桃花阵"]()
	elseif string.find("山道", _room) and string.find("天山", _zone) then
		exec("tianshan1") --开天山quest 见story.lua
	elseif (string.find(_room, "船") or string.find(_room, "木筏")) and not string.find(_room, "渔船") then

	elseif _room == "水中" then

	elseif _room == "华山绝顶" or _room == "华山之巅" then
		check_busy(function()
			exec("yun jingli;climb down")
			check_busy(function()
				exec("gps")
			end)
		end)
	elseif _room == "通天" then
		wait(3, function()
			if var["roomname"] and var["roomname"] == "通天" then
				if var["ttt_leave"] and var["ttt_leave"] == 1 then
					exec("job_escape")
				else
					var["do_stop"] = 1
					log_to_txt("fadai", var.id)
					local id = var["char_id"] or "none"
					local os_date = os.date("%m/%d %H:%M:%S")
					echo("\n" .. C.c .. "<解密>:" .. os_date .. C.W .. "【玩家:" .. id .. "】：通天塔出现异常，开始重启")
					var["ttt_root"] = 1
					check_busy(function()
						exec("tongtianta_continue;up;look")
					end)
				end
			end
		end)
	elseif _room == "情怀战舰" or _room == "舰底海水" then
		var["do_stop"] = 1
		log_to_txt("fadai", var.id)
		local id = var["char_id"] or "none"
		local os_date = os.date("%m/%d %H:%M:%S")
		echo("\n" .. C.c .. "<解密>:" .. os_date .. C.W .. "【玩家:" .. id .. "】：海战副本出现异常，开始重启")
		var["battleship_root"] = 1
		check_busy(function()
			exec("climb;otherquest_battleship;look")
			set_timer("timer1", 3, function()
				send("location")
			end)
		end)
	elseif _room == "情怀岛" then
		var["do_stop"] = 1
		log_to_txt("fadai", var.id)
		local id = var["char_id"] or "none"
		local os_date = os.date("%m/%d %H:%M:%S")
		echo("\n" .. C.c .. "<解密>:" .. os_date .. C.W .. "【玩家:" .. id .. "】：海战副本出现异常，开始重启")
		var["battleship_root"] = 1
		var["killer_master_number"] = 0
		Close_battleship_triggers()
		open_trigger("battleship_8")
		open_trigger("battleship_9")
		open_trigger("battleship_12")
		open_trigger("battleship_13")
		open_trigger("battleship_14")
		open_trigger("battleship_15")
		open_trigger("battleship_16")
		open_trigger("battleship_17")
		open_trigger("battleship_18")
		del_timer("timer")
		del_timer("timer1")
		del_timer("timer2")
		check_busy(function()
			exec("otherquest_battleship")
			close_trigger("battleship_4")
			exec("look")
		end)
	elseif _room == "铁舟上" then
		send("wield tiejiang")
		send("hua boat")
	elseif _room == "最后乐园" then
		check_busy(function()
			send("down")
			exec("gps")
		end)
	elseif _room == "中军辕门" or _room == "中军" or _room == "中军大帐" then

	elseif migong_rooms[_zone .. _room] then
		migong_rooms[_zone .. _room]()
	else
		-- 默认随机移动
		local _run = get_random_move(var["roomexit"])
		send(_run)
		exec("gps")
	end
end)

-- GPS定位成功处理函数
add_alias("gps_ok", function(params)
	var["migong"] = 0
	var["wrong_way"] = 0
	local gps = var["gps"]
	local _room = var["roomname"] or "none"
	local _zone = var["roomzone"] or "none"

	-- 处理特殊位置的后续动作
	if string.find(_zone, "伊犁城") and string.find("铁铺|商铺|城中心|巴依家院|客栈|客栈二楼", var["roomname"]) then
		exec("d;se;e;e;w;s;look")
		wait(1, function(params)
			var["wrong_way"] = 0
			if string.find(var["roomexit"], "south") then
				exec("s;gps")
			else
				set_dazuo("yili_wait")
				exec("go_dazuo")
			end
		end)
	elseif _room == "洗象池边" then
		wait(2, function()
			exec("s;ed;gps")
		end)
	elseif _room == "八卦桃花阵" then
		wait(2, function()
			exec("w;w;gps")
		end)
	elseif string.find("陷阱", _room) then
		send("climb up")
		exec("gps")
	elseif _room == "谷底水潭" or _room == "水底通道" then
		check_busy(function(params)
			exec("drop stone;drop stone;qian up;pa up;gps")
		end)
	elseif _room == "水潭表面" and gps ~= 3886 then
		exec("drop stone;drop stone;qian up;pa up;gps")
	else
		-- 处理路径规划
		if gps == 2848 or gps == 2879 then
			-- 福州南门吊桥，强制夜间模式
			var["must_be_night"] = true
		end

		local _a = pathfrom(gps, 51, lua_flags)
		local _b = pathfrom(gps, 51, lua_flags)

		if _a == "" and _b == "" then
			-- 从房间51和52出发都没路径，随机移动
			check_busy(function()
				exec("random_move;gps")
			end)
		else
			-- 有路径，执行自动叫杀检查
			after_gps = after_gps or function() end
			clear_auto_kill(after_gps)
		end
	end
end)

-------------------------------------------------------------------------------------
--------------------------------检查自动叫杀-----------------------------------------
-- 自动叫杀检查触发器1：检查大部分NPC是否不在
add_trigger("check_auto_kill_1",
	"^[ > ]*(:nnnnnnnnnnnnn|zhiqin bing|huilang|xue bao|jiao zhong|dog|bai xiong|ye lang|lang|yang xiao|shi zhe|lao hu|ma zei|xingxiu dizi|chuchen zi|huizhen zunzhe|zhuye qing)\\s+不在这里",
	function(params)
		close_scattered_triggers("check_auto_kill", { 1, 2 })
		unset_timer("timer")
		after_check_auto_kill()
	end)

-- 自动叫杀检查触发器2：特殊处理snake
add_trigger("check_auto_kill_2", "^[ > ]*snake\\s+不在这里", function(params)
	set_timer("timer", 1, function()
		send("kill lao hu")
		send("compare lao hu")
	end)
end)

-- 初始化时关闭自动叫杀触发器
close_scattered_triggers("check_auto_kill", { 1, 2 })

--树林深处|密林|林间道|雪积古道|灵獒宫|前殿|藏经阁二楼|天山脚下|龙王殿|针叶林|千古石|百丈泉|玉屏瀑|观瀑亭|卧云崖|碧血崖|仙人指路|仙人洞|一品崖|五老峰|入胜亭|紫云崖|桃花川|紫石崖|眉毛崖|三岔口|星宿海|石道|烈火旗|洪水旗|厚土旗|锐金旗|巨木旗|了望台|北城墙|西城墙|东城墙|南城墙|箭楼|落叶丛林|积雪丛林

-- 自动叫杀NPC配置表
auto_kill_config = {
	-- 针叶林：头狼、灰狼
	["针叶林"] = {
		npcs = { "toulang", "huilang" },
		compare_npc = "huilang"
	},
	-- 华山松树林/树林：无需叫杀
	["华山松树林"] = { special = "华山_skip" },

	-- 野狼区域
	["野狼区域"] = {
		rooms = "千古石|百丈泉|玉屏瀑|观瀑亭|卧云崖|碧血崖|仙人指路|仙人洞|一品崖|五老峰|入胜亭|紫云崖|桃花川|紫石崖|眉毛崖|三岔口",
		npcs = { "ye lang" },
		compare_npc = "ye lang"
	},

	-- 星宿海区域：出尘子
	["星宿海区域"] = {
		rooms = "星宿海|石道",
		npcs = { "chuchen zi" },
		compare_npc = "chuchen zi",
		party_check = "星宿派"
	},

	-- 明教旗帜区域：教众
	["明教旗帜区域"] = {
		rooms = "烈火旗|洪水旗|厚土旗|锐金旗|巨木旗",
		npcs = { "jiao zhong" },
		compare_npc = "jiao zhong",
		party_check = "明教"
	}
}

-- 执行自动叫杀的通用函数
local function execute_auto_kill(config)
	open_scattered_triggers("check_auto_kill", { 1, 2 })
	if config.compare_npc then
		send("compare " .. config.compare_npc)
	end
	set_timer("timer", 1, function()
		for _, npc in ipairs(config.npcs) do
			send("kill " .. npc)
		end
		if config.compare_npc then
			send("compare " .. config.compare_npc)
		end
	end)
end

-- 检查是否有自动叫杀的NPC
function check_auto_kill(_zone, _room, _action)
	-- 设置回调函数
	function after_check_auto_kill()
		_action()
	end

	_zone = _zone or "none"
	_room = _room or "none"
	var["party"] = var["party"] or ""

	-- 检查主要自动叫杀区域
	if string.find("树林深处|密林|林间道|雪积古道|灵獒宫|前殿|藏经阁二楼|天山脚下|龙王殿|针叶林|千古石|百丈泉|玉屏瀑|观瀑亭|卧云崖|碧血崖|仙人指路|仙人洞|一品崖|五老峰|入胜亭|紫云崖|桃花川|紫石崖|眉毛崖|三岔口|星宿海|石道|烈火旗|洪水旗|厚土旗|锐金旗|巨木旗", _room) then
		if string.find("针叶林", _room) then
			execute_auto_kill(auto_kill_config["针叶林"])
		elseif (string.find("松树林", _room) or string.find("树林", _room)) and string.find("华山", _zone) then
			-- 华山松树林/树林无需叫杀
			_action()
		elseif string.find(auto_kill_config["野狼区域"].rooms, _room) then
			execute_auto_kill(auto_kill_config["野狼区域"])
		elseif string.find(auto_kill_config["星宿海区域"].rooms, _room) and not string.find(var["party"], auto_kill_config["星宿海区域"].party_check) then
			execute_auto_kill(auto_kill_config["星宿海区域"])
		elseif string.find(auto_kill_config["明教旗帜区域"].rooms, _room) and not string.find(var["party"], auto_kill_config["明教旗帜区域"].party_check) then
			execute_auto_kill(auto_kill_config["明教旗帜区域"])
		elseif string.find("龙王殿", _room) and not string.find(var["party"], "明教") then
			execute_auto_kill({ npcs = { "yang xiao" }, compare_npc = "yang xiao" })
		elseif string.find("天山脚下", _room) and not string.find(var["party"], "星宿派") then
			execute_auto_kill({ npcs = { "xingxiu dizi" }, compare_npc = "xingxiu dizi" })
		elseif string.find("藏经阁二楼", _room) then
			execute_auto_kill({ npcs = { "huizhen zunzhe" }, compare_npc = "huizhen zunzhe" })
		elseif string.find("前殿", _room) then
			execute_auto_kill({ npcs = { "shi zhe" }, compare_npc = "shi zhe" })
		elseif string.find("灵獒宫", _room) then
			execute_auto_kill({ npcs = { "dog" }, compare_npc = "dog" })
		elseif string.find("雪积古道", _room) then
			execute_auto_kill({ npcs = { "xue bao" }, compare_npc = "xue bao" })
		elseif string.find("林间道", _room) then
			execute_auto_kill({ npcs = { "zhuye qing" }, compare_npc = "zhuye qing" })
		elseif string.find("密林", _room) then
			execute_auto_kill({ npcs = { "ma zei" }, compare_npc = "ma zei" })
		elseif string.find("树林深处", _room) then
			execute_auto_kill({ npcs = { "snake" }, compare_npc = "snake" })
		else
			-- 无叫杀NPC
			_action()
		end
	elseif string.find("树林", _room) and string.find("明教", _zone) then
		-- 明教树林：蛇
		execute_auto_kill({ npcs = { "snake" }, compare_npc = "snake" })
	elseif string.find("了望台|北城墙|西城墙|东城墙|南城墙|箭楼", _room) then
		-- 城墙区域：执勤兵
		execute_auto_kill({ npcs = { "zhiqin bing" }, compare_npc = "zhiqin bing" })
	elseif string.find("落叶丛林", _room) then
		-- 落叶丛林：野猪、巨蟒、蛇
		execute_auto_kill({ npcs = { "ye zhu", "ju mang", "snake" }, compare_npc = "snake" })
	elseif string.find("积雪丛林", _room) then
		-- 积雪丛林：豹子、白熊
		execute_auto_kill({ npcs = { "bao zi", "bai xiong" }, compare_npc = "bai xiong" })
	elseif string.find("松树林", _room) and not string.find("华山|嵩山少林", _zone) then
		-- 松树林（非华山、嵩山）：老虎
		execute_auto_kill({ npcs = { "lao hu" }, compare_npc = "lao hu" })
	else
		-- 无叫杀NPC
		_action()
	end
end

-- 迷宫房间处理配置表
-- 包含各种迷宫和特殊地形的脱离方法
migong_rooms = {
	-- 昆仑山云杉林：随机选择两种路径之一
	["昆仑山云杉林"] = function()
		local r = math.random(2)
		if r == 1 then
			exec("e;s;w;gps")
		else
			exec("s;w;gps")
		end
	end,

	-- 明教树林：固定路径脱离
	["明教树林"] = function()
		wait1(12, function()
			exec("#6 w;sw;w;gps")
		end)
	end,

	-- 明教紫杉林：关闭迷宫触发器后脱离
	["明教紫杉林"] = function()
		close_scattered_triggers("maze", { 21, 22, 29, 30 })
		wait1(10, function()
			exec("se;#3 w;se;gps")
		end)
	end,

	-- 明教小沙丘：西北方向脱离
	["明教小沙丘"] = function()
		wait1(10, function()
			exec("w;w;w;nw;gps")
		end)
	end,

	-- 星宿海星宿海：向南脱离
	["星宿海星宿海"] = function()
		wait1(15, function()
			exec("#9 s;gps")
		end)
	end,

	-- 铁掌山松树林：固定路径脱离
	["铁掌山松树林"] = function()
		wait1(10, function()
			exec("s;e;s;w;s;gps")
		end)
	end,
	["星宿海清水温泉"] = function()
		add_alias("after_faint", function()
			exec("after_faint2")
		end)
		exec("ne;gps")
	end,
	["星宿海南疆沙漠"] = function()
		add_alias("after_faint", function()
			exec("after_faint2")
		end)
		if string.find(var["roomnearby"], "吐谷浑伏俟城") then
			exec("nw;sw;se;ne;gps")
		elseif string.find(var["roomnearby"], "清水温泉") then
			exec("ne;gps")
		else
			send("look southeast")
			wait(3, function()
				if string.find(var["roomnearby"], "清水温泉") then
					exec("se;ne;gps")
				else
					exec("sw;se;ne;gps")
				end
			end)
		end
	end,
	["天山山道"] = function()
		wait1(15, function()
			--exec("#15 e;gps")
			exec("tianshan1") --开天山quest 见story.lua
		end)
	end,

	["蒙古沙漠"] = function()
		wait1(18, function()
			local r = math.random(10)
			if r == 5 then
				exec("#10 s;drink jiudai;gps")
			else
				exec("#10 n;drink jiudai;gps")
			end
		end)
	end,
	["兰州城沙漠"] = function()
		wait1(18, function()
			local r = math.random(10)
			if r == 5 then
				exec("#10 s;drink jiudai;gps")
			else
				exec("#10 n;drink jiudai;gps")
			end
		end)
	end,
	["兰州沙漠"] = function()
		wait1(18, function()
			local r = math.random(10)
			if r == 5 then
				exec("#10 s;drink jiudai;gps")
			else
				exec("#10 n;drink jiudai;gps")
			end
		end)
	end,
	--
	["天龙寺松树林"] = function()
		wait1(20, function()
			exec("w;s;w;#11 s;gps")
		end)
	end,
	["长安城长街"] = function()
		wait1(18, function()
			exec("#11 w;gps")
		end)
	end,
	["无量山大松林"] = function()
		--exec("n;e;w;s;gps")
		exec("crosswl2") --maze.lua
	end,
	["无量山荆棘林"] = function()
		wait1(12, function()
			exec("s;w;#4 s;gps")
		end)
	end,
	["终南山黑林"] = function()
		wait1(15, function()
			exec("#8 w;gps")
		end)
	end,


	----

	["武当山小径"] = function()
		exec("#2 n;gps")
	end,
	["嵩山少林竹林"] = function()
		local exits = var["roomexit"] or ""
		if string.find(exits, "southeast") and string.find("|" .. exits .. "|", "|south|") then
			exec("south;gps")
		elseif string.find(exits, "southeast") then
			local r = math.random(2)
			if r == 1 then
				wait1(16, function()
					exec("se;n;s;w;e;w;e;e;s;w;n;nw;n;gps")
				end)
			else
				exec("nw;n;gps")
			end
		else
			local r = math.random(3)
			if r == 1 then
				wait1(16, function()
					exec("w;nw;w;s;se;s;e;e;ne;n;sw;n;gps") --1/3的机会先尝试通用路径
				end)
			else
				wait1(16, function()
					local r = math.random(10)
					if r == 1 then
						exec("n;s;w;e;w;e;e;s;w;n;nw;n;gps")
					elseif r == 2 then
						exec("s;w;e;w;e;e;s;w;n;nw;n;gps")
					elseif r == 3 then
						exec("w;e;w;e;e;s;w;n;nw;n;gps")
					elseif r == 4 then
						exec("e;w;e;e;s;w;n;nw;n;gps")
					elseif r == 5 then
						exec("w;e;e;s;w;n;nw;n;gps")
					elseif r == 6 then
						exec("e;e;s;w;n;nw;n;gps")
					elseif r == 7 then
						exec("e;s;w;n;nw;n;gps")
					elseif r == 8 then
						exec("s;w;n;nw;n;gps")
					elseif r == 9 then
						exec("w;n;nw;n;gps")
					else
						exec("n;nw;n;gps")
					end
				end)
			end
		end
		-----------
	end,

	["嵩山少林练武场"] = function()
		wait1(10, function()
			exec("n;n;e;e;s;e;gps")
		end)
	end,
	["古墓派石室"] = function()
		wait1(10, function()
			exec("out;#6 e;#6 s;gps")
		end)
	end,
	["终南山石室"] = function()
		wait1(10, function()
			exec("out;#6 e;#6 s;gps")
		end)
	end,
	--
	--out;#6 e;#6 s

	["嵩山少林塔林"] = function()
		wait1(15, function()
			--local r=math.random(5)
			--if r==1 then
			--exec("s;ne;se;n;e;sw;e;ne;se;s;se;open door;e;sw;n;enter;gps")
			local r = math.random(11)
			if r == 1 then
				exec("ne;se;n;e;sw;e;ne;se;s;se;open door;e;gps")
			elseif r == 2 then
				exec("se;n;e;sw;e;ne;se;s;se;open door;e;gps")
			elseif r == 3 then
				exec("n;e;sw;e;ne;se;s;se;open door;e;gps")
			elseif r == 4 then
				exec("e;sw;e;ne;se;s;se;open door;e;gps")
			elseif r == 5 then
				exec("sw;e;ne;se;s;se;open door;e;gps")
			elseif r == 6 then
				exec("e;ne;se;s;se;open door;e;gps")
			elseif r == 7 then
				exec("ne;se;s;se;open door;e;gps")
			elseif r == 8 then
				exec("se;s;se;open door;e;gps")
			elseif r == 9 then
				exec("s;se;open door;e;gps")
			elseif r == 10 then
				exec("se;open door;e;gps")
			elseif r == 11 then
				exec("open door;e;gps")
			end
		end)
	end,
	["嵩山少林回廊"] = function()
		wait1(10, function()
			exec("e;e;s;e;gps")
		end)
	end,

	------
	["福州城渔船"] = function()
		wait1(10, function()
			exec("#5 w;out;gps")
		end)
	end,
	["杭州城长廊"] = function()
		wait1(10, function()
			exec("n;s;e;w;gps")
		end)
	end,
	["杭州城柳林"] = function()
		wait1(8, function()
			exec("#3 n;gps")
		end)
	end,
	["归云庄九宫桃花阵"] = function()
		wait(2.5, function()
			exec(
			"n;n;w;w;get 32 taohua;get 16 taohua;get 8 taohua;get 4 taohua;get 2 taohua;get 1 taohua;s;get 32 taohua;get 16 taohua;get 8 taohua;get 4 taohua;get 2 taohua;get 1 taohua;s;get 32 taohua;get 16 taohua;get 8 taohua;get 4 taohua;get 2 taohua;get 1 taohua")
			wait(2.5, function()
				exec(
				"e;get 32 taohua;get 16 taohua;get 8 taohua;get 4 taohua;get 2 taohua;get 1 taohua;n;get 32 taohua;get 16 taohua;get 8 taohua;get 4 taohua;get 2 taohua;get 1 taohua;n;get 32 taohua;get 16 taohua;get 8 taohua;get 4 taohua;get 2 taohua;get 1 taohua")
				wait(2.5, function()
					exec(
					"e;get 32 taohua;get 16 taohua;get 8 taohua;get 4 taohua;get 2 taohua;get 1 taohua;s;get 32 taohua;get 16 taohua;get 8 taohua;get 4 taohua;get 2 taohua;get 1 taohua;s;get 32 taohua;get 16 taohua;get 8 taohua;get 4 taohua;get 2 taohua;get 1 taohua")
					wait(2.5, function()
						exec(
						"drop 5 taohua;n;drop 5 taohua;n;drop 5 taohua;w;drop 5 taohua;s;drop 5 taohua;s;drop 5 taohua;w;drop 5 taohua;n;drop 5 taohua;n;drop 5 taohua;gps")
					end)
				end)
			end)
		end)
	end,

	["绝情谷竹林"] = function()
		--var["path_after"]="gps"
		wait1(10, function()
			local r = math.random(4)
			if r == 1 then
				exec("s;su;wd;gps")
			elseif r == 2 then
				exec("e;su;wd;gps")
			elseif r == 3 then
				exec("w;su;wd;gps")
			else
				exec("n;su;wd;gps")
			end
		end)
	end,

	["华山松树林"] = function()
		wait1(8, function()
			exec("n;e;e;e;gps")
		end)
	end,

	["襄阳郊外树林"] = function()
		exec("crossxtj gps")
	end,

	["大雪山岩石"] = function()
		exec("tiao up;ask di yun about 离开;gps")
	end,
	--岩石 -


	-----
	["大草原沼泽"] = function()
		wait1(10, function()
			exec("#3 w;n;w;e;gps")
		end)
	end,
	["兰州城沙漠"] = function()
		wait1(16, function()
			exec("#5 s;#5 n;drink jiu dai;gps")
		end)
	end,
	["大雪山牧场"] = function()
		wait1(10, function()
			exec("w;w;s;s;e;gps")
		end)
	end,
	["峨嵋山古德林"] = function()
		wait1(8, function()
			exec("n;s;s;gps")
		end)
	end,
	["峨嵋山冷杉林"] = function()
		wait1(10, function()
			exec("#3 se;sw;gps")
		end)
	end,
	["峨嵋山九老洞"] = function()
		wait1(15, function()
			exec("#2 drop fire;#8 leave;out;gps")
		end)
	end,
	["峨嵋山九十九道弯"] = function()
		wait1(10, function()
			exec("ne;ed;ne;ed;ne;gps")
		end)
	end,
	["丐帮杏子林"] = function()
		wait1(6, function()
			exec("#2 s;gps")
		end)
	end,


	----------
	["星宿海大沙漠"] = function()
		wait1(10, function()
			exec("#5 e;gps")
		end)
	end,
	["回疆大戈壁"] = function()
		wait1(18, function()
			exec("#11 s;drink jiu dai;gps")
		end)
	end,

	["回疆针叶林"] = function()
		wait1(14, function()
			local ado = var["ado"] or 0
			--local ado=1
			local r = math.random(4)
			if r == 1 then
				if ado == 1 then
					send("ado e|e|e|e|e|e|e|e|e|e")
				else
					exec("#10 e")
				end
			elseif r == 2 then
				if ado == 1 then
					send("ado s|s|s|s|s|s|s|s|s|s")
				else
					exec("#10 s")
				end
			elseif r == 3 then
				if ado == 1 then
					send("ado w|w|w|w|w|w|w|w|w|w")
				else
					exec("#10 w")
				end
			else
				if ado == 1 then
					send("ado n|n|n|n|n|n|n|n|n|n")
				else
					exec("#10 n")
				end
			end

			exec("gps")
		end)
	end,
	["福州城山路"] = function()
		wait1(8, function()
			exec("e;ne;ne;ne;ne;e;ne;ne;gps")
		end)
	end,
	["回疆黑石围子"] = function()
		wait1(14, function()
			exec("sw;e;e;w;w;w;w;w;gps")
		end)
	end,
	["回疆大草原"] = function()
		wait1(10, function()
			exec("e;e;w;w;w;w;w;gps")
		end)
	end,
	["大草原草海"] = function()
		wait1(12, function()
			exec("#3 w;n;w;e;gps")
		end)
	end,
	--梅林
	["梅庄梅林"] = function()
		--var["path_after"]="gps"
		exec("crossmzb")
	end,
}
add_alias("leave_migong", function(params)
	local _name = var["roomname"] or ""
	local _zone = var["roomzone"] or ""
	local _name = _name .. _zone
	local _run = get_random_move(var["roomexit"])

	if migong_rooms[_name] then
		migong_rooms[_name]()
	else
		send(_run)
		exec("gps")
	end
end)
close_trigger("gps_1")

--离开wuliang大松林


--从story.lua 搬过来吧
add_alias("tianshan1", function(params) --天山quest1
	--你喃喃骂道：「这灯可有点儿邪门。」
	add_trigger("tianshan_quest_1", "你喃喃骂道：「这灯可有点儿邪门。」", function(params)
		del_trigger("tianshan_quest_1")
		local id = var["char_id"] or "none"
		local os_date = os.date("%m/%d %H:%M:%S")
		echo("\n" .. C.c .. "<解密>:" .. os_date .. C.R .. "【玩家:" .. id .. "】：天山灯火解密失败：没有发现灯火来源")
		var["quest_tianshan"] = "no"
	end)
	add_trigger("tianshan_quest_2", "你凝目向山谷望去，只见那灯火发出绿油油的光芒，迥不同寻常灯火的色作", function(params)
		local id = var["char_id"] or "none"
		local os_date = os.date("%m/%d %H:%M:%S")
		echo("\n" .. C.c .. "<解密>:" .. os_date .. C.x .. "【玩家:" .. id .. "】：你凝目向山谷望去，只见那灯火发出绿油油的光芒")
		var["quest_tianshan"] = "yes"
		exec("tianshan2") --打开天山2 触发
	end)
	add_trigger("tianshan_quest_4", "你发现有邪魔外道在此聚会，赶紧躲到旁边的岩石中躲了起来。", function(params)
		del_trigger("tianshan_quest_4")
		local id = var["char_id"] or "none"
		local os_date = os.date("%m/%d %H:%M:%S")
		echo("\n" .. C.c .. "<解密>:" .. os_date .. C.x .. "【玩家:" .. id .. "】：来到山谷，看到乌老大等群魔聚会")
		send("look")
	end)
	add_trigger("tianshan_quest_5", "又奔了一阵，脚底下踏到薄薄的积雪，原来已奔到山腰。", function(params)
		del_trigger("tianshan_quest_5")
		local id = var["char_id"] or "none"
		local os_date = os.date("%m/%d %H:%M:%S")
		echo("\n" .. C.c .. "<解密>:" .. os_date .. C.x .. "【玩家:" .. id .. "】：逃到山腰")
		send("look")
	end)
	add_trigger("tianshan_quest_3", "^[ > ]*你把\\s+\"action\"\\s+设定为\\s+\"天山乱闯看看", function(params)
		if var["quest_tianshan"] == nil then
			check_busy(function()
				exec("#6 e;n;#6 w")
				wait(3, function()
					exec("alias action 天山乱闯看看...")
				end)
			end)
		elseif var["quest_tianshan"] == "no" then
			exec("gps")
		elseif var["quest_tianshan"] == "yes" then --成功
			del_trigger("tianshan_quest_1")
			del_trigger("tianshan_quest_2")
			del_trigger("tianshan_quest_3")
			unset_timer("wait")
			check_busy(function()
				set_timer("wait", 120, function()
					exec("open bu dai")
				end)
			end)
		end
	end)
	check_busy(function()
		exec("pray pearl;#6 e;n;#6 w")
		wait(3, function()
			exec("alias action 天山乱闯看看...")
		end)
	end)
end)

add_alias("tianshan2", function(params) --天山quest2
	add_trigger("tianshan_quest_6", "声，传出一下苍老的呼痛之声，正是你刚才听到的那个声音。", function(params)
		local id = var["char_id"] or "none"
		local os_date = os.date("%m/%d %H:%M:%S")
		echo("\n" .. C.c .. "<解密>:" .. os_date .. C.x .. "【玩家:" .. id .. "】：听到呼痛声")
		unset_timer("wait")
		check_busy(function()
			exec("open bu dai")
			check_busy(function()
				exec("ask nv tong about name")
				local id = var["char_id"] or "none"
				local os_date = os.date("%m/%d %H:%M:%S")
				echo("\n" .. C.c .. "<解密>:" .. os_date .. C.x .. "【玩家:" .. id .. "】：天山灯火询问女童名字")
			end)
		end)
	end)
	add_trigger("tianshan_quest_7", "若不是念在你相救有功，姥姥一掌早便送了你的狗命", function(params)
		local id = var["char_id"] or "none"
		local os_date = os.date("%m/%d %H:%M:%S")
		echo("\n" .. C.c .. "<解密>:" .. os_date .. C.Y .. "【玩家:" .. id .. "】：开始磕头")
		set_timer("timer", 1.5, function()
			exec("ketou tonglao")
		end)
	end)
	add_trigger("tianshan_quest_9", "天山童姥道：“你总算对姥姥恭谨有礼。姥姥一来要利用于你，二来嘉奖后辈，", function(params)
		local id = var["char_id"] or "none"
		local os_date = os.date("%m/%d %H:%M:%S")
		echo("\n" .. C.c .. "<解密>:" .. os_date .. C.Y .. "【玩家:" .. id .. "】：磕满6个，开始传授折梅手和六阳掌")
		send("xixi")
	end)
	add_trigger("tianshan_quest_8", "突然你听得身后蹄声急促，夹着叮当、叮当的铃声，回头望去，但见数十匹马急驰而至。", function(params)
		del_trigger("tianshan_quest_6")
		del_trigger("tianshan_quest_7")
		del_trigger("tianshan_quest_8")
		del_trigger("tianshan_quest_9")
		unset_timer("timer")
		check_busy(function()
			exec("#10 n;gps")
			local id = var["char_id"] or "none"
			local os_date = os.date("%m/%d %H:%M:%S")
			echo("\n" .. C.c .. "<解密>:" .. os_date .. C.M .. "【玩家:" .. id .. "】：天山灯火全部流程结束")
		end)
	end)
end)
--  野狼(Ye lang)



-- 危险NPC配置表：中文名称到英文ID的映射
dangerousnpc = {
	-- 野生动物类
	["疯狗"] = "feng gou",
	["头狼"] = "toulang",
	["灰狼"] = "huilang",
	["野狼"] = "ye lang",
	["毒蛇"] = "du she",
	["老虎"] = "lao hu",
	["玉蜂"] = "yu feng",
	["竹叶青"] = "zhuye qing",
	["雪豹"] = "xue bao",
	["豹子"] = "bao zi",
	["野猪"] = "ye zhu",
	["怪蟒"] = "guai mang",
	["巨蟒"] = "ju mang",
	["毒蟒"] = "du mang",
	["恶犬"] = "e quan",
	["蜈蚣"] = "wu gong",
	["大白熊"] = "bai xiong",
	["白熊"] = "bai xiong",
	["黑色毒蛇"] = "heise dushe",

	-- 盗贼类
	["马贼"] = "ma zei",

	-- 武林人物类
	["梅超风"] = "mei chaofeng",
	["朱聪"] = "zhu cong",
	["干光豪"] = "gan guanghao",
	["葛光佩"] = "ge guangpei",

	-- 军队将领类
	["折冲将军"] = "zhechong jiangjun",
	["平寇将军"] = "pingkou jiangjun",
	["征东将军"] = "zhengdong jiangjun",
	["车骑将军"] = "cheqi jiangjun",

	-- 门派人物类
	["慧真尊者"] = "huizhen zunzhe",
	["杨逍"] = "yang xiao",
	["出尘子"] = "chuchen zi",
	["执法使者"] = "shi zhe",

	-- 教众帮众类
	["厚土旗教众"] = "jiao zhong",
	["巨木旗教众"] = "jiao zhong",
	["锐金旗教众"] = "jiao zhong",
	["烈火旗教众"] = "jiao zhong",
	["洪水旗教众"] = "jiao zhong",
	["黑衣帮众"] = "bangzhong",
	["灰衣帮众"] = "bangzhong",
}
-- 清除自动叫杀NPC函数
function clear_auto_kill(_action)
	if var["debug"] > 0 then
		echo(C.W .. "进入clear_auto_kill!")
	end

	local _obj = var["roomobj"] or {}
	local _exist = false
	local name
	local npcname

	-- 检查房间内是否有危险NPC
	for k, v in pairs(_obj) do
		npcname = string.match(k, "」(%S+)$") or string.match(k, "位(%S+)$") or
			string.match(k, "只(%S+)$") or string.match(k, "条(%S+)$") or
			string.match(k, " (%S+)$") or "none"

		if var["debug"] > 0 then
			echo(C.W .. npcname .. "=" .. v)
		end

		-- 检查是否为危险NPC（通过ID和中文名称双重验证）
		local dangerous_ids =
		"|feng gou|yu feng|mei chaofeng|caihua she|e quan|du mang|guai mang|gan guanghao|ge guangpei|shi zhe|xue bao|chuchen zi|yang xiao|xingxiu dizi|huizhen zunzhe|jiao zhong|shi zhe|zhiqin bing|ju mang|ye zhu|bai xiong|bao zi|lao hu|snake|du she|ma zei|zhuye qing|huilang|toulang|pingkou jiangjun|cheqi jiangjun|zhechong jiangjun|zhengdong jiangjun|heise dushe|snake|ye lang|huiyi bangzhong|heiyi bangzhong|bangzhong|zhu cong|wu gong|"
		local dangerous_names =
		"|疯狗|玉蜂|菜花蛇|梅超风|怪蟒|毒蟒|恶犬|黑衣帮众|灰衣帮众|朱聪|干光豪|葛光佩|马贼|执法使者|执勤兵|白熊|大白熊|竹叶青|征东将军|折冲将军|车骑将军|平寇将军|老虎|豹子|雪豹|野猪|头狼|野狼|灰狼|黑色毒蛇|毒蛇|巨蟒|出尘子|杨逍|范遥|星宿派弟子|慧真尊者|厚土旗教众|巨木旗教众|洪水旗教众|烈火旗教众|锐金旗教众|蜈蚣|"

		if string.find(dangerous_ids, "|" .. v .. "|") and string.find(dangerous_names, "|" .. npcname .. "|") then
			_exist = true
			name = v
			break
		end
	end

	if _exist then
		if var["debug"] > 0 then
			echo("\n" .. C.W .. "发现自动叫杀npc，开始清除!")
		end
		do_log("log", "进入清除自动叫杀，npc_id为" .. name)

		-- 设置清除完成触发器
		add_trigger("check_auto_kill_1",
			"^[ > ]*(:nnnnnnnnnnnnn|caihua she|heiyi bangzhong|huiyi bangzhong|heise dushe|snake|du she|feng gou|yu feng|caihua she|mei chaofeng|du mang|guai mang|e quan|bangzhong|wu gong|zhu cong|gan guanghao|ge guangpei|zhiqin bing|shi zhe|toulang|huilang|lang|bao zi|xue bao|jiao zhong|dog|ye lang|lang|yang xiao|fan yao|shi zhe|lao hu|ma zei|xingxiu dizi|chuchen zi|huizhen zunzhe|zhuye qing)\\s+不在这里",
			function(params)
				close_scattered_triggers("check_auto_kill", { 1, 2, 3 })
				unset_timer("timer")
				_action()
			end)

		-- 设置NPC不理你的触发器
		add_trigger("check_auto_kill_3", "^[ > ]*(你发现.*根本不理你！|看清楚一点，那并不是活物。)", function(params)
			close_scattered_triggers("check_auto_kill", { 1, 2, 3 })
			unset_timer("timer")
			_action()
		end)

		-- 设置特殊NPC连锁触发器
		add_trigger("check_auto_kill_2", "^[ > ]*(?:nnnnnnnnnnn|ju mang|ye zhu|bai xiong)\\s+不在这里", function(params)
			if string.find(params[-1], "ye zhu") then
				set_timer("timer", 1, function()
					open_scattered_triggers("check_auto_kill", { 1, 2, 3 })
					send("halt")
					send("kill ju mang")
					send("compare ju mang")
				end)
			elseif string.find(params[-1], "ju mang") then
				set_timer("timer", 1, function()
					open_scattered_triggers("check_auto_kill", { 1, 2, 3 })
					send("halt")
					send("kill du she")
					send("compare du she")
				end)
			elseif string.find(params[-1], "bai xiong") then
				set_timer("timer", 1, function()
					open_scattered_triggers("check_auto_kill", { 1, 2, 3 })
					send("halt")
					send("kill bao zi")
					send("compare bao zi")
				end)
			end
		end)

		-- 根据NPC类型执行不同的攻击策略
		local speed = var["script_speed"] or 1
		speed = 1 / speed

		if name == "pingkou jiangjun" or name == "cheqi jiangjun" or name == "zhechong jiangjun" or name == "zhengdong jiangjun" then
			-- 将军类：攻击多只狗
			exes("kill dog;kill dog 2;kill dog 3;kill dog 4;compare dog")
			set_timer("timer", speed, function()
				open_scattered_triggers("check_auto_kill", { 1, 2, 3 })
				exes("kill dog;kill dog 2;kill dog 3;kill dog 4;compare dog")
			end)
		elseif name == "du she" or name == "heise dushe" or name == "snake" then
			-- 蛇类：特殊处理
			exes("kill snake;compare snake")
			set_timer("timer", speed, function()
				open_scattered_triggers("check_auto_kill", { 1, 2, 3 })
				exes("kill snake;compare snake")
			end)
		else
			-- 普通NPC：直接攻击
			exes("kill " .. name .. ";compare " .. name)
			set_timer("timer", speed, function()
				open_scattered_triggers("check_auto_kill", { 1, 2, 3 })
				exes("kill " .. name .. ";compare " .. name)
			end)
		end
	else
		-- 未发现叫杀NPC
		if var["debug"] > 0 then
			echo(C.W .. "未发现自动叫杀npc!\n")
		end
		close_scattered_triggers("check_auto_kill", { 1, 2, 3 })
		_action()
	end
end
