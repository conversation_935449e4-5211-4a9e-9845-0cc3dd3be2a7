-- 简单技能放弃脚本
-- 根据设置的技能列表自动放弃技能

-- 设置要放弃的技能列表，用"|"分隔
-- 例如：var["fangqi_skills_list"] = "yuxiao-jian|hamagong|force"
var["fangqi_skills_list"] = var["fangqi_skills_list"] or ""

local fangqi_list = {}

-- 解析放弃技能列表
function parse_fangqi_list()
    fangqi_list = {}
    if var["fangqi_skills_list"] and var["fangqi_skills_list"] ~= "" then
        for skill in string.gmatch(var["fangqi_skills_list"], "([^|]+)") do
            skill = string.gsub(skill, "^%s*(.-)%s*$", "%1")  -- 去除空格
            if skill ~= "" then
                table.insert(fangqi_list, skill)
            end
        end
    end
end

-- 检查并删除技能
function check_and_delete()
    parse_fangqi_list()

    if #fangqi_list == 0 then
        echo("\n" .. C.r .. "放弃技能列表为空！")
        return
    end

    echo("\n" .. C.W .. "开始检查技能...")

    -- 使用现有的技能数据
    local skills_name = var["skills_name"] or {}
    local skills_id = var["skills_id"] or {}

    -- 找出需要删除的技能
    local to_delete = {}
    for _, skill in ipairs(fangqi_list) do
        local skill_id = nil

        -- 先检查是否直接是英文ID（在skills_name中存在）
        if skills_name[skill] then
            skill_id = skill
        else
            -- 根据中文名在skills_id中查找英文ID
            skill_id = skills_id[skill]
        end

        if skill_id then
            table.insert(to_delete, skill_id)
            echo("\n" .. C.r .. "发现需要放弃的技能：" .. skill .. " (ID:" .. skill_id .. ")")
        else
            echo("\n" .. C.W .. "技能 " .. skill .. " 未拥有，跳过")
        end
    end

    if #to_delete == 0 then
        echo("\n" .. C.g .. "没有需要放弃的技能")
        return
    end

    -- 只放弃第一个技能
    local skill = to_delete[1]
    echo("\n" .. C.y .. "正在放弃技能：" .. skill)
    send("fangqi " .. skill)
end

-- 开始检查技能
add_alias("fangqi_skills", function()
    if not var["fangqi_skills_list"] or var["fangqi_skills_list"] == "" then
        echo("\n" .. C.r .. "请先设置放弃技能列表！")
        echo("\n" .. C.W .. "例如：var[\"fangqi_skills_list\"] = \"yuxiao-jian|hamagong\"")
        return
    end

    -- 直接检查并删除，不需要抓取
    check_and_delete()
end)