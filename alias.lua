--alias.lua 模仿zmud 命令解析器#alias
var={}--变量表var["xxx"]="yyy"
alias = alias or {}--alias 表 

--1)执行客户端送来指令cmd
mb.client_aliases=function(cmd) 
	if var["debug"] and var["debug"]>3 then 
		echo("\n"..C.y.."输入指令："..cmd)
	end

	for match in string.gmatch(cmd..";","(.-);") do
		if string.match(match,"(#%d+)%s-.+") then
		local alias_times=string.match(match,"#(%d+)%s.+")
		local alias_action=string.match(match,"#%d+%s(.+)")
			for i=1,times do
				if string.match(alias_action,"(%S+)") and string.match(alias_action,"(%S+)")~="" and alias[string.match(alias_action,"(%S+)")] then
					local i=0
					local params={"","","","","","","","","",""}
					for match in string.gmatch(alias_action,"(%S+)") do
                        params[i]=expand(match)
                        i=i+1
					end

					if alias[params[0]] then
                        params[-1] = string.match(alias_action,"^"..params[0].." (.+)")                            
						if params[-1] then  params[-1]= expand(params[-1]) end
							alias[params[0]](params)
							return true
					end
				else
						return false
				end
			end
		else
			if string.match(match,"(%S+)") and string.match(match,"(%S+)")~="" and alias[string.match(match,"(%S+)")] then
					local i=0
					local matchs
					local params={"","","","","","","","","",""}
					for matchs in string.gmatch(match,"(%S+)") do
                        params[i]=expand(matchs)
                        i=i+1
					end

					if alias[params[0]] then
                        params[-1] = string.match(match,"^"..params[0].." (.+)")                            
						if params[-1] then  params[-1]= expand(params[-1]) end
							alias[params[0]](params)
							return true
					end
				else
						return false
				end
		end
	end

end

--2)增加alias
function add_alias(cmd,alias_function)
        alias[cmd]=alias_function
end

--3)删除alias
function del_alias(cmd)
        alias[cmd]=nil
end

--4)执行cmd 
function exec_alias(cmd)
        if string.match(cmd,"(%S+)") and string.match(cmd,"(%S+)")~="" and alias[string.match(cmd,"(%S+)")] then
                local i=0
                local params={"","","","","","","","","",""}
                for match in string.gmatch(cmd,"(%S+)") do
                        params[i]=expand(match)
                        i=i+1
                end
                if alias[params[0]] then
						if 1==0 or (var["debug"] and var["debug"]>4) then echo("\n"..C.W.."Alias:"..C.Y..params[0]) end
                        params[-1] = string.match(cmd,"^"..params[0].." (.+)")                            
						if params[-1] then  params[-1]= expand(params[-1]) end
                        alias[params[0]](params)
                end
        else
                if var["debug"] and var["debug"]>0 then 
                        echo(C.y..cmd) 
                end
                        send(expand(cmd))
        end
end

--5)分解解析alias指令by";",然后执行,可以解析#10 w
function exec(cmd)
        for match in string.gmatch(cmd..";","(.-);") do
                if string.match(match,"(#%d+)%s-.+") then
                local times=string.match(match,"#(%d+)%s.+")
                local action=string.match(match,"#%d+%s(.+)")
                        for i=1,times do
                                exec_alias(action)
                        end
                else
                        exec_alias(match)
                end
        end
end

function exes(cmd,t)
		if t~=nil then
			alarm("input",t,function() --启用一个input 计时器，时间为t，收到触发请及时，del_timer("input")
				exes(cmd,t)
			end)
		end
        for match in string.gmatch(cmd..";","(.-);") do
                if string.match(match,"(#%d+)%s-.+") then
                local times=string.match(match,"#(%d+)%s.+")
                local action=string.match(match,"#%d+%s(.+)")
                        for i=1,times do
                                exec_alias(action)
                        end
                else
                        exec_alias(match)
                end
        end
end


--6)展开变量by @
function expand(cmd)
	if string.match(cmd,"@%S+") then
		cmd=string.gsub(cmd,"@([%w_]+)",var) --变量只能由字母 数字 下划线组成，其他不认识，比如@hha_1
		cmd=string.gsub(cmd,"@([%w_]+)","")  --没找到的var 为空
		return cmd
	else
		return cmd
	end
end

--定义几个alias自己用 查看trigger var alias
--listt 查看触发trigger
--listv 查看变量var
--lista 查看alias
--listi 查看物品item
--lists 查看skills
add_alias("test_smy",function()
	for i=1,10 do  --10 个技能和对应的Pfm，应该够用了吧？
		if var["smy_sp_skill"..i]==nil then --如果未设置特殊pfm，那么强制设置为空字符串 防止比较过程中出错
			var["smy_sp_skill"..i]=""
		end
		echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."【战颂摩崖】：特殊技能列表【"..var["smy_sp_skill"..i].."】")
	--	if var["smy_sp_skill"..i]==var["wushi1_skill"] then  
	--		sp_pfm_exist=true  											--特殊技能有匹配，那么不能用默认值
	--		send("alias pfm "..expand(var["smy_sp_pfm"..i]))  		--特殊技能对应特殊pfm如果有匹配的话，那么pfm设置为对应的特殊pfm
	--		send("alias pfm_backup "..expand(var["smy_sp_pfm"..i]))
	--	end
	end
end)
add_alias("listt",function(params)
        echo("\n"..C.x.."以下触发trigger")
        for k,v in pairs(trig_rex) do
                echo(C.g.."#trigger id: "..k.." == rex: "..v) 
        end
        echo(C.x.."以上触发trigger")
end)
add_alias("listv",function(params)
        echo("\n"..C.x.."以下变量var")
        for k,v in pairs(var) do
                echo(C.c..'#var "'..k..'" ==  "'..tostring(v)..'"') 
        end
        echo(C.x.."以上变量var")
end)
add_alias("lista",function(params)
        echo("\n"..C.x.."以下别名alias")
        for k,v in pairs(alias) do
                echo(C.y..'#alias "'..k..'" ==  "'..tostring(v)..'"') 
        end
		if alias["crossemghz"]==nil then
			echo(C.x.."无孤鸿子路径")
		end
		if alias["crossemghz"]~=nil then
			echo(C.x.."有孤鸿子路径")
		end
        echo(C.x.."以上别名alias")
end)
add_alias("listi",function(params)
        echo("\n"..C.x.."以下item")
        for k,v in pairs(item) do
                echo(C.y..'#item "'..k..'" ==  "'..tostring(v)..'"') 
        end
        echo(C.x.."以上item")
end)
--
add_alias("lists",function(params)
        echo("\n"..C.x.."以下skills")
		var["skills"]=var["skills"] or {}
		var["skills_name"]=var["skills_name"] or {}
		var["skills_id"]=var["skills_id"] or {}
		var["skills_level"]=var["skills_level"] or {}
		var["skills_jifa"]=var["skills_jifa"] or {}
		var["skills_jifaid"]=var["skills_jifaid"] or {}
		var["skills_jifaname"]=var["skills_jifaname"] or {}
		var["skills_jifalevel"]=var["skills_jifalevel"] or {}
        for k,v in pairs(var["skills"]) do
                echo(C.y..'skills "'..k..'" ==  "'..tostring(v)..'"') 
        end
		echo("\n"..C.x.."名称".."\n")
		 for k,v in pairs(var["skills_name"]) do
                echo(C.y..'name "'..k..'" ==  "'..tostring(v)..'"') 
        end
		echo("\n"..C.x.."id".."\n")
		 for k,v in pairs(var["skills_id"]) do
                echo(C.y..'id "'..k..'" ==  "'..tostring(v)..'"') 
        end
		echo("\n"..C.x.."level".."\n")
		 for k,v in pairs(var["skills_level"]) do
                echo(C.y..'level "'..k..'" ==  "'..tostring(v)..'"') 
        end
				echo("\n"..C.x.."jifa".."\n")
		 for k,v in pairs(var["skills_jifa"]) do
                echo(C.y..'jifa "'..k..'" ==  "'..tostring(v)..'"') 
        end
				echo("\n"..C.x.."jifa id".."\n")
		 for k,v in pairs(var["skills_jifaid"]) do
                echo(C.y..'jifaid "'..k..'" ==  "'..tostring(v)..'"') 
        end
				echo("\n"..C.x.."jifa_name".."\n")
		 for k,v in pairs(var["skills_jifaname"]) do
                echo(C.y..'jifaname "'..k..'" ==  "'..tostring(v)..'"') 
        end
				echo("\n"..C.x.."jifa level".."\n")
		 for k,v in pairs(var["skills_jifalevel"]) do
                echo(C.y..'jifalevel "'..k..'" ==  "'..tostring(v)..'"') 
        end
        echo(C.x.."以上skills")
end)
add_alias("un_timer",function(params) --删除定时器
		del_timer("timer")
		del_timer("wait")
		del_timer("wait1")
		del_timer("alarm")
		del_timer("check_busy_1")
		del_timer("check_busy_2")
		del_timer("checkidle")
		del_timer("checkidle_mods")
end)
add_alias("getcolor",function(params)

echo("\n")
-- 颜色测试优化版本
local function test_colors()
    local color_codes = {"d", "r", "g", "y", "b", "m", "c", "x", "D", "R", "G", "Y", "B", "M", "C", "W", "k", "v", "u"}
    
    for _, code in ipairs(color_codes) do
        echo('"..C.'..code..'.."'.."="..C[code]..'【颜色测试】')
    end
end

-- 调用函数
test_colors()

end)
Print("--- 加载模块: 别名 ---")