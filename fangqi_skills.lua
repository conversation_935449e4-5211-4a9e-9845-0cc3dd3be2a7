-- 简单技能放弃脚本
-- 根据设置的技能列表自动放弃技能

-- 设置要放弃的技能列表，用"|"分隔
-- 例如：var["fangqi_skills_list"] = "yuxiao-jian|hamagong|force"
var["fangqi_skills_list"] = var["fangqi_skills_list"] or ""

-- 当前技能列表（从skills命令获取）
local current_skills = {}
local fangqi_list = {}
local current_index = 1

-- 获取技能列表的触发器
add_trigger("get_skills_list", "^[ > ]*(.+)\\s+\\((.+)\\)\\s+-\\s+(.+)\\s+(\\d+)/", function(params)
    local skill_name = params[2]  -- 技能英文名
    current_skills[skill_name] = true
end)

-- 技能列表结束触发器
add_trigger("skills_end", "^[ > ]*你现在身上共有 (\\d+) 点经验", function(params)
    del_trigger("get_skills_list")
    del_trigger("skills_end")
    
    -- 开始检查并删除技能
    check_and_delete()
end)

-- 放弃技能确认
add_trigger("fangqi_confirm", "^[ > ]*你确定要放弃.*技能吗？", function(params)
    send("y")
end)

-- 解析放弃技能列表
function parse_fangqi_list()
    fangqi_list = {}
    if var["fangqi_skills_list"] and var["fangqi_skills_list"] ~= "" then
        for skill in string.gmatch(var["fangqi_skills_list"], "([^|]+)") do
            skill = string.gsub(skill, "^%s*(.-)%s*$", "%1")  -- 去除空格
            if skill ~= "" then
                table.insert(fangqi_list, skill)
            end
        end
    end
end

-- 检查并删除技能
function check_and_delete()
    parse_fangqi_list()
    
    if #fangqi_list == 0 then
        echo("\n" .. C.r .. "放弃技能列表为空！")
        return
    end
    
    echo("\n" .. C.W .. "开始检查技能...")
    
    -- 找出需要删除的技能
    local to_delete = {}
    for _, skill in ipairs(fangqi_list) do
        if current_skills[skill] then
            table.insert(to_delete, skill)
            echo("\n" .. C.r .. "发现需要放弃的技能：" .. skill)
        end
    end
    
    if #to_delete == 0 then
        echo("\n" .. C.g .. "没有需要放弃的技能")
        return
    end
    
    -- 开始删除
    current_index = 1
    delete_next_skill(to_delete)
end

-- 删除下一个技能
function delete_next_skill(to_delete)
    if current_index > #to_delete then
        echo("\n" .. C.g .. "技能删除完成！")
        return
    end
    
    local skill = to_delete[current_index]
    echo("\n" .. C.y .. "正在放弃技能：" .. skill)
    send("fangqi " .. skill)
    
    current_index = current_index + 1
    
    -- 延迟删除下一个
    set_timer("delete_next", 2, function()
        delete_next_skill(to_delete)
    end)
end

-- 开始检查技能
add_alias("fangqi_skills", function()
    if not var["fangqi_skills_list"] or var["fangqi_skills_list"] == "" then
        echo("\n" .. C.r .. "请先设置放弃技能列表！")
        echo("\n" .. C.W .. "例如：var[\"fangqi_skills_list\"] = \"yuxiao-jian|hamagong\"")
        return
    end
    
    current_skills = {}  -- 清空技能列表
    echo("\n" .. C.W .. "正在获取当前技能列表...")
    send("skills")
end)