
-- padright函数，从var表中抓取变量并右边填充空格
-- s: 变量名, len: 总字符长度，如果变量字符不够就用空格填充
padright = function(s, len)
    local str = ""
    if var[s] then
        str = tostring(var[s])
    else
        str = "无"
    end

    -- 确保len是数字类型
    len = tonumber(len)
    if not len or len <= 0 then
        return str
    end

    -- 计算当前变量值的字符数
    local current_len = string.len(str)

    -- 如果当前字符数已经达到或超过目标长度，直接返回
    if current_len >= len then
        return str
    end

    -- 计算需要填充的空格数量，在右边填充空格
    local spaces_needed = len - current_len
    return str .. string.rep(" ", spaces_needed)
end

-- bracketvar函数，显示自定义符号包围的变量值，并根据总长度进行右填充
-- 参数:
--   var_name: 变量名
--   total_width: 总宽度（可选，默认不填充）
--   left_symbol: 左符号（可选，默认"【"）
--   right_symbol: 右符号（可选，默认"】"）
-- 功能:
--   1. 显示 左符号+变量值+右符号 格式
--   2. 计算总长度（包括左右符号）
--   3. 如果总长度不够total_width，就在右符号后面加空格
-- 示例:
--   bracketvar("name") -> 【张三】
--   bracketvar("name", 10) -> 【张三】    （后面补空格到10位）
--   bracketvar("name", 10, "[", "]") -> [张三]     （自定义[]符号）
--   bracketvar("name", 10, "<", ">") -> <张三>     （自定义<>符号）
--   bracketvar("name", 10, "", "") -> 张三      （无符号，纯变量值）
bracketvar = function(var_name, total_width, left_symbol, right_symbol)
    if not var_name or var_name == "" then
        return ""
    end

    -- 默认符号
    left_symbol = left_symbol or ""
    right_symbol = right_symbol or ""

    -- 获取变量值
    local var_value = ""
    if var[var_name] then
        var_value = tostring(var[var_name])
    else
        var_value = "无"
    end

    -- 构建 左符号+变量值+右符号 格式
    local result = left_symbol .. var_value .. right_symbol

    -- 如果指定了总宽度，进行右填充
    if total_width and tonumber(total_width) and tonumber(total_width) > 0 then
        local target_width = tonumber(total_width)
        local current_length = string.len(result)  -- 计算总长度

        if current_length < target_width then
            -- 在右符号后面加空格，直到达到目标宽度
            local spaces_needed = target_width - current_length
            result = result .. string.rep(" ", spaces_needed)
        end
    end

    return result
end

--[[
================================================================================
  通用行抓取公共函数 (Generic Line Capture Public Function)
================================================================================
--]]

--[[
  功能：创建一个通用的行抓取处理器。
  这是函数它不直接抓取，而是根据你的配置生成一个抓取器。

  @param config (table): 一个包含以下键的配置表：
    - name (string): 抓取任务的唯一名称，用于生成全局变量(var)的键，必须唯一。
    - start_pattern (string): 启动抓取的触发器正则表达式。
    - stop_condition (function): 停止条件函数 `func(line, lines_table)`。
                                 每抓取一行后调用，当此函数返回 true 时，抓取过程停止。
                                 'line' 是当前原始行, 'lines_table' 是已抓取行的表格。
    - line_processor (function, optional): 行处理器函数 `func(line, lines_table)`。
                                             在添加行之前对每一行进行处理。
                                             如果返回 nil 或 false，则该行被跳过。
                                             如果返回一个字符串，则该字符串被存入结果。
                                             如果未提供此函数，则默认存储原始行。
    - start_callback (function, optional): 启动回调函数 `func(params, start_func)`。
                                             当'start_pattern'匹配成功时调用。
                                             'params'是匹配结果，'start_func'是内部的启动函数。
                                             此回调用于在正式启动抓取前进行预处理（例如，从 params 提取数量）。
                                             你必须在此回调中手动调用 start_func(...) 来启动抓取，并可以传入参数。
    - formatter (function): 格式化函数 `func(lines_table, format_type)`。
                            用于将最终抓取到的数据（lines_table）格式化为用户想看到的字符串。
--]]
function create_line_capture_handler(config)
    -- 使用 config.name 构造独立的 var 键，防止不同抓取器之间冲突
    local var_prefix = "capture_handler_" .. config.name
    local capturing_flag = var_prefix .. "_capturing"
    local trigger_active_flag = var_prefix .. "_trigger_active"
    local lines_table_key = var_prefix .. "_lines"
    local start_args_key = var_prefix .. "_start_args"
    local trigger_name = var_prefix .. "_collect_trigger"

    -- 内部函数：启动抓取过程
    local function start_capture(...)
        if var[trigger_active_flag] then
            del_trigger(trigger_name)
        end

        -- 初始化状态变量
        var[capturing_flag] = true
        var[lines_table_key] = {}
        var[trigger_active_flag] = true
        var[start_args_key] = {...} -- 保存启动时传入的参数（例如技能总数）

        -- 添加核心的行收集触发器
        add_trigger(trigger_name, "^.*$", function()
            if not var[capturing_flag] then return end

            local raw_line = getrawline()
            local line_to_store = raw_line

            -- 使用行处理器对行进行预处理
            if config.line_processor then
                line_to_store = config.line_processor(raw_line, var[lines_table_key])
            end

            -- 如果处理器返回有效值（不是nil或false），则存入表格
            if line_to_store and line_to_store ~= "" then
                table.insert(var[lines_table_key], line_to_store)
            end

            -- 检查是否满足停止条件
            if config.stop_condition(raw_line, var[lines_table_key]) then
                var[capturing_flag] = false
                var[trigger_active_flag] = false
                del_trigger(trigger_name)
            end
        end)
    end

    -- 设置启动触发器，这是整个过程的入口
    add_trigger(config.name .. "_start_trigger", config.start_pattern, function(params)
        if config.start_callback then
            -- 如果有自定义回调，则通过它来启动
            config.start_callback(params, start_capture)
        else
            -- 否则直接启动
            start_capture()
        end
    end)

    -- 返回一个包含公共接口的表
    local public_interface = {}

    -- 公共接口函数：获取格式化后的数据
    function public_interface.get_data(format_type)
        format_type = format_type or "detailed"
        var[lines_table_key] = var[lines_table_key] or {}
        -- 调用用户定义的格式化函数
        return config.formatter(var[lines_table_key], format_type)
    end

    return public_interface
end

--[[
================================================================================
  技能抓取部分
================================================================================
]]

-- 要过滤的技能关键字列表
local basic_skills_to_filter = {
    "基本", "经脉学", "讨价还价", "本草术理", "禅宗心法", "毒技", "奇门八卦", "道学心法", "读书写字", "锻造术", "织造术",
    "正气诀", "大乘佛法", "琴棋书画", "叫化绝活", "打狗棒诀", "道听途说", "道德经", "驻颜术", "五行阵"
}

-- 1. 配置并创建技能抓取处理器
local skills_handler = create_line_capture_handler({
    name = "special_skills",
    start_pattern = "^你目前学过(\\S+)种技能：",

    -- 启动回调：从触发器参数中提取技能总数，并传递给抓取核心
    start_callback = function(params, start_func)
        local count_str = params[1]
        local count = trans(count_str) -- 'trans' 用于转换中文数字的函数
        start_func(count) -- 将数量作为参数传递，以便在 stop_condition 中使用
    end,

    -- 停止条件：当抓取的行数达到预期数量时停止
    stop_condition = function(line, lines_table)
        -- 从启动时保存的参数中获取预期数量
        local expected_count = (var["capture_handler_special_skills_start_args"] or {0})[1]
        return #lines_table >= (expected_count or 0)
    end,

    -- 格式化：过滤并格式化技能列表
    formatter = function(lines, format_type)
        if #lines == 0 then return C.y .. "暂无抓取的技能数据" end

        local filtered_lines = {}
        for _, line in ipairs(lines) do
            local should_be_filtered = false
            for _, filter_keyword in ipairs(basic_skills_to_filter) do
                if string.find(line, filter_keyword) then
                    should_be_filtered = true
                    break
                end
            end
            if not should_be_filtered then
                table.insert(filtered_lines, line)
            end
        end

        if #filtered_lines == 0 then return C.y .. "所有抓取的技能均被过滤，无数据显示" end

        local result = ""
        if format_type == "detailed" then
            result = "\n" .. C.c .. string.rep(" ", 22) .. C.Y .. "【特殊技能列表】"..#filtered_lines .. "个特殊技能\n"
            result = result .. C.x .. string.rep("=", 80) .. "\n"
        end

        for i, line in ipairs(filtered_lines) do
            if format_type == "detailed" then
                result = result .. C.g .. line .. C.x .. "\n"
            else
                result = result .. line
                if i < #filtered_lines then result = result .. "\n" end
            end
        end
        --[[
        if format_type == "detailed" then
            result = result .. C.x .. string.rep("=", 80) .. "\n"
        end
        --]]
        return result
    end
})

-- 2. 创建一个全局函数来调用处理器的 get_data 方法
get_special_skills = function(format_type)
    return skills_handler.get_data(format_type)
end

--[[
================================================================================
  nstory 抓取部分
================================================================================
]]

-- 1. 配置并创建 nstory 抓取处理器
local nstory_handler = create_line_capture_handler({
    name = "nstory",
    start_pattern = "^你的解密全记录：",

    -- 行处理器：不抓取起始行本身
    line_processor = function(line, lines_table)
        if string.find(line, "你的解密全记录：") then
            return false -- 返回 false 以跳过此行
        end
        return line -- 返回原始行进行存储
    end,

    -- 停止条件：当遇到结束标志行时停止
    stop_condition = function(line, lines_table)
        return string.find(line, "└──┴────────────────────┘")
    end,

    -- 格式化器：格式化解密记录列表
    formatter = function(lines, format_type)
        if #lines == 0 then return C.y .. "暂无抓取的解密记录数据" end

        local result = ""

        for i, line in ipairs(lines) do
            if format_type == "detailed" then
                result = result .. line .. C.x .. "\n"
            else
                result = result .. line
                if i < #lines then result = result .. "\n" end
            end
        end
        --[[
        if format_type == "detailed" then
            result = result .. C.x .. string.rep("=", 80) .. "\n"
            result = result .. C.x .. "共显示 " .. C.y .. #lines .. C.x .. " 行解密记录"
        end
        --]]
        return result
    end
})

-- 2. 创建一个全局函数来调用处理器的 get_data 方法
get_nstory = function(format_type)
    return nstory_handler.get_data(format_type)
end

--[[
================================================================================
  谣言抓取部分
================================================================================
]]

-- 定义存储表和数量上限
local rumor_storage = {}
local max_rumors = 11

-- 配置并创建 rumor 抓取处理器
local rumor_handler = create_line_capture_handler({
    name = "rumor_storage_with_custom_replace", -- 使用新名称
    start_pattern = "^>*\\s*【谣言】",

    start_callback = function(params, start_func)
        -- a. 抓取原始行
        local raw_line = getrawline()

        -- b. 【核心】调用 replace 函数！
        local cleaned_rumor = replace(raw_line, "> ", "", 1)

        -- c. 执行列表管理逻辑
        if #rumor_storage >= max_rumors then
            table.remove(rumor_storage, 1)
        end
        table.insert(rumor_storage, cleaned_rumor)
    end,
    
    formatter = function(lines, format_type)
        if #rumor_storage == 0 then
            return C.y .. "暂无抓取的谣言数据"
        end

        format_type = format_type or "detailed"
        local result = ""


        for i, rumor in ipairs(rumor_storage) do
             if format_type == "detailed" then
                 result = result .. rumor .. C.x .. "\n"
             else
                 result = result .. rumor .. (i < #rumor_storage and "\n" or "")
             end
        end


        return result
    end
})

get_rumor = function(format_type)
    return rumor_handler.get_data(format_type)
end
--[[
================================================================================
  log抓取部分
================================================================================
]]
-- 定义用于存储抓取文本的表和数量上限
local captured_texts = {}
local max_captured_texts = 33 -- 您可以根据需要调整此上限

-- 配置并创建文本抓取处理器
local text_capture_handler = create_line_capture_handler({
    name = "colored_text_capture", -- 为处理器命名

    -- 使用正则表达式匹配多种开头的文本
    -- ^\s* 匹配行首的任意空格
    -- (<Lua>|<解密>) 匹配关键字中的任意一个
    start_pattern = "^>*\\s*(<解密>|<Lua>)",

    start_callback = function(params, start_func)
        -- a. 抓取原始行内容
        local raw_line = getrawline()
        -- b. 定义一个精确的日期时间模式，用于被移除
        --    模式含义: MM/DD HH:MM:SS，以及后面可能跟的空格
            local datetime_pattern = "%d%d/%d%d"
        -- c. 对抓取到的原始行进行清理，移除可能存在的月日
        local cleaned_line = replace(raw_line, datetime_pattern, "")

        -- d. 执行列表管理逻辑，如果超过上限则移除最早的一条
        if #captured_texts >= max_captured_texts then
            table.remove(captured_texts, 1)
        end

        -- e. 将清理后的文本插入到存储表中
        table.insert(captured_texts, cleaned_line)
    end,

    -- 格式化输出函数，用于显示抓取到的内容
    formatter = function(lines, format_type)
        -- 如果没有抓取到任何内容，则返回提示信息
        if #captured_texts == 0 then
            return C.y .. "暂无抓取的log数据" -- C.y 是颜色代码，表示黄色
        end

        format_type = format_type or "detailed" -- 默认使用 "detailed" 格式
        local result = ""

        -- 遍历所有抓取到的文本并格式化输出
        for i, text in ipairs(captured_texts) do
             if format_type == "detailed" then
                 -- 详细模式下，每行末尾都添加换行和颜色重置代码
                 result = result .. text .. C.x .. "\n" -- C.x 是颜色重置代码
             else
                 -- 简洁模式下，仅在非最后一行末尾添加换行符
                 result = result .. text .. (i < #captured_texts and "\n" or "")
             end
        end

        return result
    end
})

-- 定义一个全局函数，方便调用以获取抓取到的文本
get_log = function(format_type)
    return text_capture_handler.get_data(format_type)
end