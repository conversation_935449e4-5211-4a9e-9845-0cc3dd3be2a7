 --
--^???你不会这种技能。
--^???格式： lingwu
--^???你从实战中得到的潜能已经用完了。
--^???你的*造诣不够，无法领悟更深一层的基本
--^???你的{内力|真气}不够。
--^???你没办法集中精神。
--^???你只能从特殊技能中领悟。
--^???你瞑思苦想，对*的体会又深了一层。
--^???以你现在的基本内功修为，尚无法领悟
--^???由于实战经验不足，阻碍了你的「*」进步！

add_alias("usepot_lingwu",function(params) --使用潜能学习
	local lingwu=var["lingwu"] or 0
	local lingwuover=var["lingwuover"] or 0
--	local lingwu_count=var["lingwu_count"] or 1
	var["lingwu_count"]=1
	close_fight()--关闭战斗触发
	var["fight"]=0 --不在战斗状态
	var["idle"]=0 --没发呆
	
	local keeplingwu=var["keeplingwu"] or 1
	local maxpot=var["maxpot"] or 1
	local lingwuoverpot=var["lingwuoverpot"] or 0
	local party=var["party"] or "none"
	if lingwu==0 then
		exec("fail_usepot") --使用潜能失败
	else
		if lingwuover==1 then --领悟已经结束过
			if keeplingwu==1 and maxpot>lingwuoverpot then
				var["lingwuover"]=0
				var["lingwuoverpot"]=maxpot
				if string.find(party,"少林派") then --少林要去一下戒律院逛逛
					add_alias("after_shaolin_usepot",function(params)
						exec("go_lingwu")
					end)
					exec("check_vip check_shaolin_usepot")
				else
					exec("check_vip go_lingwu") --go_lingwu 领悟吧	
				end
			else
				exec("fail_usepot") --使用潜能失败
			end
		else
			var["lingwuover"]=0
				if string.find(party,"少林派") then
					add_alias("do_shaolin_usepot",function(params)
						exec("go_lingwu")
					end)
					exec("check_vip check_shaolin_usepot")
				else
					exec("check_vip go_lingwu") --go_lingwu 领悟吧	
				end
		end
	end
end)

add_alias("go_lingwu",function(params) --使用潜能学习
	var["port"]=563
	function after_gps()
		add_alias("after_faint",function(params)
			exec("lingwu_skills")--设置领悟晕倒
		end)
	
		function after_goto()
		add_trigger("lingwu_start","^\\s*你把\\s+\"action\"\\s+设定为\\s+\"Start lingwu",function(params)
			del_trigger("lingwu_start")
			del_timer("input")
			exec("lingwu_skills")
		end)
		local skills_bei2=var["skills_bei2"] or ""
		local r=math.random(2)
		if r==1 then
		send("alias bei_skills "..var["skills_bei2"])
		else
		send("alias bei_skills "..var["skills_bei1"])
		end
		wait1(15,function()
		send("bei_skills")
		send("cha")
		send("jifa")
		do_log("lingwu_start")
		exes("alias action Start lingwu...",5)
		end)
		
		end
		exec("goto @port")
	end
	exec("gps")
end)

add_alias("lingwu_skills",function(params) --使用潜能学习读书写字
	local lingwu_count=var["lingwu_count"] or 1
	local lingwu_limit=num_item(var["lingwu_skills"])
	
	if lingwu_count>lingwu_limit then
		var["lingwuover"]=1
		var["lingwuoverpot"]=var["maxpot"]
		close_lingwu()
		close_lianxi()
		exec("after_usepot")
	else
		open_lingwu()
		close_lianxi()
		set_dazuo("lingwu")
		local xuexi_yun="yun xinjing"
		local jifa_force=var["jifa_force"] or "none"
		if string.find(jifa_force,"yunu") or string.find(jifa_force,"qiantian") or string.find(jifa_force,"kurong") then
			xuexi_yun="yun xinjing"
		elseif string.find(jifa_force,"linji") then
			xuexi_yun="yun zhixin"
		elseif string.find(jifa_force,"chaosheng") then
			xuexi_yun="yun qimen"
		elseif string.find(jifa_force,"beiming") then
			xuexi_yun="yun beiming"
		end
		var["xuexi_yun"]=xuexi_yun
--		var["xuenum"]=var["xuenum"] or 15
--		vlingwu_count"]=var["lingwu_count"] or 1
		local lingwuweapon=var["lingwuweapon"] or ""
		exec("unwield_weapon;unwield @other_weapon")
		send("wield "..lingwuweapon)

			local lingwu_skills=var["lingwu_skills"] or ""
			local lingwu_count=var["lingwu_count"] or 1
			local lingwu_skill=get_item(lingwu_skills,lingwu_count) --function.lua 模仿zmud %item
			--加入xuantie-jianfa&sword&changjian格式支持
			if string.find(lingwu_skill,"&") then
				if string.find(lingwu_skill,"&.*&") then --yingou-bifa&brush&brush
					local lingwu_jifa=string.match(lingwu_skill,"^(.-)&.*")
					lingwu_skill=string.match(lingwu_skill,"^.-&(.*)&.*")
					check_busy2(function()
						send("bei none")
						send("jifa "..lingwu_skill.." "..lingwu_jifa)	
						lingwu_action()
					end)
				else --xuantie-jianfa&sword
					local lingwu_jifa=string.match(lingwu_skill,"^(.*)&.*")
					lingwu_skill=string.match(lingwu_skill,"^.*&(.*)")
					check_busy2(function()
						send("bei none")
						send("jifa "..lingwu_skill.." "..lingwu_jifa)
						lingwu_action()
					end)
				end
			else
				lingwu_action()
			end			

	end
end)

function lingwu_action()

	--	echo("\n"..C.x.."<Debug>:lingwu "..lingwu_skill)
		local very_slow_walk=var["very_slow_walk"] or 0
		local wait_time=0.6
		if very_slow_walk==1 then 
			wait_time=1
		elseif very_slow_walk==2 then
			wait_time=1.5
		end
		
		set_timer("timer",wait_time,function()
			if var["xuexi_yun"] and var["xuexi_yun"]=="" then
			exec("yun jing")
			else

				end
				local lingwu_skills=var["lingwu_skills"] or ""
				local lingwu_count=var["lingwu_count"] or 1
				local lingwu_skill=get_item(lingwu_skills,lingwu_count)
					if string.find(lingwu_skill,"&") then
						if string.find(lingwu_skill,"&.*&") then --yingou-bifa&brush&brush
							local lingwu_jifa=string.match(lingwu_skill,"^(.-)&.*")
							lingwu_skill=string.match(lingwu_skill,"^.-&(.*)&.*")			
						else --xuantie-jianfa&sword
							local lingwu_jifa=string.match(lingwu_skill,"^(.*)&.*")
							lingwu_skill=string.match(lingwu_skill,"^.*&(.*)")
						end
					end
				send("lingwu "..lingwu_skill)
				exec("@xuexi_yun;yun jing")
	--			send("lingwu "..lingwu_skill)
				local myexp=var["exp"] or 150000
				if myexp>100000000 then
					send("lingwu "..lingwu_skill)
					send("lingwu "..lingwu_skill)
					send("lingwu "..lingwu_skill)
					send("lingwu "..lingwu_skill)
					send("lingwu "..lingwu_skill)
					send("lingwu "..lingwu_skill)
				elseif myexp>20000000 then
					send("lingwu "..lingwu_skill)
					send("lingwu "..lingwu_skill)
					send("lingwu "..lingwu_skill)
					send("lingwu "..lingwu_skill)
					send("lingwu "..lingwu_skill)
					send("lingwu "..lingwu_skill)
					send("lingwu "..lingwu_skill)
					send("lingwu "..lingwu_skill)
				elseif myexp>2000000 then
					send("lingwu "..lingwu_skill)
					send("lingwu "..lingwu_skill)
					send("lingwu "..lingwu_skill)
					send("lingwu "..lingwu_skill)
					send("lingwu "..lingwu_skill)
				end
		end)

end

add_alias("lingwu_skills_next",function(params) --使用潜能学习读书写字
	local lingwu_count=var["lingwu_count"] or 1
		lingwu_count=lingwu_count+1
	local lingwu_limit=num_item(var["lingwu_skills"])
	
	if lingwu_count>lingwu_limit then
			var["lingwuoverpot"]=var["maxpot"]
	var["lingwuover"]=1
		close_lingwu()
		close_lianxi()
		exec("after_usepot")
	else
		var["lingwu_count"]=lingwu_count
		exec("lingwu_skills")
	end
end)

--
--^???你不会这种技能。
--^???格式： lingwu
--^???你从实战中得到的潜能已经用完了。
--^???你的*造诣不够，无法领悟更深一层的基本
--^???你的{内力|真气}不够。
--^???你没办法集中精神。
--^???你只能从特殊技能中领悟。
--^???你瞑思苦想，对*的体会又深了一层。
--^???以你现在的基本内功修为，尚无法领悟
--^???由于实战经验不足，阻碍了你的「*」进步！
add_trigger("lingwu_1","^\\s*格式： lingwu",function(params)
	close_lingwu()
	unset_timer("timer")
	wait(2,function()
			exec("go_lingwu")
	end)
end)
add_trigger("lingwu_2","^\\s*(?:你不会这种技能。|你只能从特殊技能中领悟。|以你现在的基本内功修为，尚无法领悟|由于实战经验不足，阻碍了你的「)",function(params)

			local lingwu_skills=var["lingwu_skills"] or ""
			local lingwu_count=var["lingwu_count"] or 1
			local lingwu_skill=get_item(lingwu_skills,lingwu_count)
			
			--加入xuantie-jianfa&sword&changjian格式支持
			if string.find(lingwu_skill,"&") then
				if string.find(lingwu_skill,"&.*&") then --yingou-bifa&brush&brush
					local lingwu_jifa=string.match(lingwu_skill,"^(.-)&.*")
					lingwu_skill=string.match(lingwu_skill,"^.-&(.*)&.*")
					send("bei none")
					send("jifa "..lingwu_skill.." "..lingwu_jifa)			
				else --xuantie-jianfa&sword
					local lingwu_jifa=string.match(lingwu_skill,"^(.*)&.*")
					lingwu_skill=string.match(lingwu_skill,"^.*&(.*)")
					send("bei none")
					send("jifa "..lingwu_skill.." "..lingwu_jifa)
				end
			end
			
			
			
	if string.find(line[1],"由于实战经验不足，阻碍了你的「基本内功") and lingwu_skill~="force" then
	
	else
			close_lingwu()
	unset_timer("timer")
	wait(2,function()
			exec("lingwu_skills_next")
	end)
	end

end)
add_trigger("lingwu_3","^\\s*你的.*造诣不够，无法领悟更深一层的基本",function(params)
	close_lingwu()
	unset_timer("timer")

	local maxpot=var["maxpot"] or 100
			maxpot=maxpot-100
	--
	local lingwu_skill,lingwu_jifa,lianxi_weapon,lingwu_mark=get_lingwu_jifa_skill(var["lingwu_skills"],var["lingwu_count"],var["skills_jifaid"])
	--
	local skills_level=var["skills_level"] or {}
	local lingwu_skill_level=skills_level[lingwu_skill] or 0
	if var["no_lian_in_lingwu"] and var["no_lian_in_lingwu"]==1 then  ---不在达摩院练技能
		wait(2,function()                                          --新加的
		exec("lingwu_skills_next")                                 --新加的
end)
else 
	if lingwu_skill=="parry" or (lingwu_skill=="force" and var["no_lian_force_in_lingwu"] and var["no_lian_force_in_lingwu"]==1) then
		if var["lian_force"] and var["lian_force"]==2 then var["lian_force"]=1 end
		wait(2,function()
			exec("lingwu_skills_next")
		end)
	else
		if lingwu_skill_level<maxpot+1 then --如果领悟skills小于maxpot-100 才能练习 ，防止死过以后还在练习
			close_lingwu()
			
						local lingwu_skills=var["lingwu_skills"] or ""
						local lingwu_count=var["lingwu_count"] or 1
						local lingwu_skill=get_item(lingwu_skills,lingwu_count)
						if string.find(lingwu_skill,"&") then
							if string.find(lingwu_skill,"&.*&") then --yingou-bifa&brush&brush
								local lingwu_jifa=string.match(lingwu_skill,"^(.-)&.*")
								lingwu_skill=string.match(lingwu_skill,"^.-&(.*)&.*")
								set_dazuo("lianxi")
								wait(1,function()
									check_busy2(function()
										send("bei none")
										send("jifa "..lingwu_skill.." "..lingwu_jifa)
										exec("s;s;unwield @lingwuweapon;lian_skills")
									end)
								end)								
							else --xuantie-jianfa&sword
								local lingwu_jifa=string.match(lingwu_skill,"^(.*)&.*")
								lingwu_skill=string.match(lingwu_skill,"^.*&(.*)")
								set_dazuo("lianxi")
								wait(1,function()
									check_busy2(function()
										send("bei none")
										send("jifa "..lingwu_skill.." "..lingwu_jifa)
										exec("s;s;unwield @lingwuweapon;lian_skills")
									end)
								end)
							end
						else
							set_dazuo("lianxi")
							wait(1,function()
									check_busy2(function()
										exec("s;s;unwield @lingwuweapon;lian_skills")
									end)
							end)
						end
		else
			wait(2,function()
				exec("lingwu_skills_next")
			end)
		end
	end
end
end)
add_trigger("lingwu_4","^\\s*你瞑思苦想，对.*的体会又深了一层。",function(params)
	var["idle"]=0
end)
add_trigger("lingwu_5","^\\s*(?:你没办法集中精神。|你的内力不够。|你的真气不够。)",function(params)
	close_lingwu()
	close_lianxi()
	unset_timer("timer")
			local dahuandan1=0
			local dahuandan2=0
			local chuanbeiwan=0
			local start_wash=var["start_wash"] or 0
			local usedrug=var["usedahuandan"] or 0--用药
			local usechuanbei=var["chuanbeiwan"] or 0
			local usedahuandan=var["dahuandan"] or 0
			local tongbao=var["tongbao"] or 0 --通宝总数
			local gold=var["gold"] or 0 --存款总数
			if not null(item) then
				dahuandan1=item["da huandan"] or 0
				dahuandan2=item["dahuan dan"] or 0
				chuanbeiwan=item["chuanbei wan"] or 0
				gold1=item["gold"] or 0 --身上的gold
			end
			if chuanbeiwan>0 then
				wait(3,function()
					exec("eat chuanbei wan;i;lingwu_skills")
				end)
			elseif var["usedahuandan"] and var["usedahuandan"]==4 and dahuandan1>0 and ((item["da huandan"] and item["da huandan"]>0) or (item["dahuan dan"] and item["dahuan dan"]>0)) then
				exec("fu dan;i;lingwu_skills")
			elseif var["usedahuandan"] and var["usedahuandan"]==4 and dahuandan2>0 and ((item["da huandan"] and item["da huandan"]>0) or (item["dahuan dan"] and item["dahuan dan"]>0)) then
				exec("fu dan;i;lingwu_skills")
			elseif var["usedahuandan"] and var["usedahuandan"]==4 and usechuanbei>4 and chuanbeiwan<1 then --chuanbei wan不够
	--			exec("changejob")
				close_lingwu()
				close_lianxi()
				unset_timer("timer")
				wait(2,function()
					exec("after_usepot")
				end)
			elseif usedrug==4 and usedahuandan>4 and dahuandan1<1 and dahuandan2<1 and tongbao>100 then --大还丹不够
	--			exec("changejob")
				close_lingwu()
				close_lianxi()
				unset_timer("timer")
				wait(2,function()
					exec("after_usepot")
				end)
			elseif start_wash==1 and gold>500 then --设置了洗澡而且存款大于1000锭金子，没钱的穷逼就算了
			    close_lingwu()
				close_lianxi()
				unset_timer("timer")
				wait(2,function()
					exec("i;score;after_wash_lingwu")
				end)	
			else
				var["idle"]=0 --万一红了一直练呢，算了先这样了
				set_dazuo("lingwu")
				open_dazuo()
				wait(1,function()
					exec("i;go_dazuo")
				end)
			end	
add_alias("after_wash_lingwu",function(params) --使用潜能
	gold1=item["gold"] or 0 --身上的gold
	if gold1<10 then
		g(653,function() --潜能银行
		 wait(1,function()
			exec("qu 20 gold;i")
			check_busy(function()
				exec("after_wash_lingwu")
			end)
		end)
	end)
	else
	exec("after_wash_start_lingwu")
  end
end)

add_alias("after_wash_start_lingwu",function(params) 
	var["idle"]=0
	--local sex=var["sex"] or ""
	g(687,function() --清池
		wait(1,function()
		      --exec("unwield_weapon")
		send("i")  --检测下武器
	     local equip=var["equipment_s"] or {}
	     for k,v in pairs(equip) do
		send("unwield "..k)
		send("unwield "..k.." 2")
	end
	       if var["sex"]=="女性" then
	       exec("give yahuan 1 gold;e;remove all;wash")
	       else
	       exec("give yahuan 1 gold;w;remove all;wash")
	        end
		check_busy(function() 
			g(686,function() --茶馆
				exec("wear all")
				 exec("go_lingwu")
				end)
			end)
		end)
	end)
end)			

	
end)
add_trigger("lingwu_6","^\\s*你从实战中得到的潜能已经用完了。",function(params)
	close_lingwu()
	close_lianxi()
	unset_timer("timer")
	wait(2,function()
		exec("after_usepot")
	end)
end)
function open_lingwu()
	open_trigger("lingwu_1")
	open_trigger("lingwu_2")
	open_trigger("lingwu_3")
	open_trigger("lingwu_4")
	open_trigger("lingwu_5")
	open_trigger("lingwu_6")
end
function close_lingwu()
	close_trigger("lingwu_1")
	close_trigger("lingwu_2")
	close_trigger("lingwu_3")
	close_trigger("lingwu_4")
	close_trigger("lingwu_5")
	close_trigger("lingwu_6")
end
close_lingwu()
--~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
--练习
--~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
--练习
--[[add_alias("lian_skills",function(params) --练习
	close_lingwu() --关闭领悟
	open_lianxi() --打开练习
	
	local lingwu_skill,lingwu_jifa,lingwu_mark=get_lingwu_jifa_skill(var["lingwu_skills"],var["lingwu_count"],var["skills_jifaid"])		
	var["lingwu_jifa"]=lingwu_jifa
	--以上设置好了
	if lingwu_mark==1 then
		check_busy2(function()
			set_timer("timer",0.8,function()
				exec("i;bei none;jifa "..lingwu_skill.." "..lingwu_jifa..";verify "..lingwu_jifa..";yun qi;lian "..lingwu_skill..";yun jingli;yun jing;lian "..lingwu_skill..";hp;alias action 技能练习中...")
			end)
		end)
	else
		set_timer("timer",0.8,function()
			exec("i;bei none;jifa "..lingwu_skill.." "..lingwu_jifa..";verify "..lingwu_jifa..";yun qi;lian "..lingwu_skill..";yun jingli;yun jing;lian "..lingwu_skill..";hp;alias action 技能练习中...")
		end)
	end
end)]]
add_alias("lian_skills",function(params) --练习
	close_lingwu() --关闭领悟
	open_lianxi() --打开练习
		local lingwu_skill,lingwu_jifa,lingwu_mark=get_lingwu_jifa_skill(var["lingwu_skills"],var["lingwu_count"],var["skills_jifaid"])		
	var["lingwu_jifa"]=lingwu_jifa
	--echo("\n"..C.W..var["fanwenbook"])
	--echo("\n"..C.W..lingwu_skill)
	--以上设置好了
	--[[if lingwu_mark==1 then
		check_busy2(function()
			set_timer("timer",0.8,function()
				exec("i;bei none;jifa "..lingwu_skill.." "..lingwu_jifa..";verify "..lingwu_jifa..";yun qi;lian "..lingwu_skill..";yun jingli;yun jing;lian "..lingwu_skill..";hp;alias action 技能练习中...")
			end)
		end)
	else]]
	if var["fanwenbook"]==1 and lingwu_skill=="force" then --有yijinjing的星宿派并且练习的是force 
		check_busy2(function()
		  set_timer("timer",0.8,function()
		  exec("i;#5 read fanwen book;yun jing;hp;alias action 技能练习中...")
		  end)
		end)
	 else
		check_busy2(function()
		  set_timer("timer",0.8,function()
		  exec("i;bei none;jifa "..lingwu_skill.." "..lingwu_jifa..";verify "..lingwu_jifa..";yun qi;lian "..lingwu_skill..";yun jingli;yun jing;lian "..lingwu_skill..";hp;alias action 技能练习中...")
		end)
	end)
end
end)

function get_skill_weapon_id(xuexi_skill) --得到skill 对应的武器id
	local xuexiweapon=""
	if string.find(xuexi_skill,"yuxiao") then
		xuexiweapon="xiao"
	elseif string.find(xuexi_skill,"jinshe") then
		xuexiweapon="yinshe sword"
	elseif string.find(xuexi_skill,"lingshe%-zhang") then
		xuexiweapon="gangzhang"
	elseif string.find(xuexi_skill,"jian") then
		xuexiweapon="changjian"
	elseif string.find(xuexi_skill,"dao") then
		xuexiweapon="blade"
	elseif string.find(xuexi_skill,"bian") then
		xuexiweapon="changbian"
	elseif string.find(xuexi_skill,"weituo") or string.find(xuexi_skill,"gun") then
		xuexiweapon="tiegun"
	elseif string.find(xuexi_skill,"zhang") or string.find(xuexi_skill,"wushang") then
		xuexiweapon="gangzhang"
	elseif string.find(xuexi_skill,"tenglong") or string.find(xuexi_skill,"lingfa") then
		xuexiweapon="ansha bishou"
	elseif string.find(xuexi_skill,"bifa") then
--		xuexiweapon="tiebi"
		xuexiweapon="panguan bi"
	elseif string.find(xuexi_skill,"bang") then
		xuexiweapon="zhubang"
	elseif string.find(xuexi_skill,"%-fu") then
		xuexiweapon="gang fu"
	elseif string.find(xuexi_skill,"xiangfu") then
		xuexiweapon="liuxing chui"
	elseif string.find(xuexi_skill,"%-fu") then
		xuexiweapon="gang fu"
	elseif string.find(xuexi_skill,"yinsuo") then
		xuexiweapon="changbian"
	elseif string.find(xuexi_skill,"%-gou") then
		xuexiweapon="hook"
	elseif string.find(xuexi_skill,"yangjia%-qiang") then
		xuexiweapon="hongying qiang"
	end
	return xuexiweapon
end

function get_lingwu_jifa_skill(lingwu_skills,lingwu_count,skills_jifaid) --获取当前领悟skill，和相应的 jifa
	local lingwu_skill=get_item(lingwu_skills,lingwu_count) --当前领悟技能，如sword
	local lingwu_jifa="noskills" --领悟技能jifa的skills，如xuantie-jianfa
	local lingwu_mark=0
	local lianxi_weapon=""
			if string.find(lingwu_skill,"&") then
				lingwu_mark=1
				if string.find(lingwu_skill,"&.*&") then --yingou-bifa&brush&brush
					lingwu_jifa=string.match(lingwu_skill,"^(.-)&.*")
					lianxi_weapon=string.match(lingwu_skill,".*&.*&(.*)$")
					lingwu_skill=string.match(lingwu_skill,"^.-&(.*)&.*")	
				else --xuantie-jianfa&sword
					lingwu_jifa=string.match(lingwu_skill,"^(.*)&.*")
					lingwu_skill=string.match(lingwu_skill,"^.*&(.*)")
				end
			else
				lingwu_skill=lingwu_skill
				if null(skills_jifaid) then
					lingwu_jifa="noskills"
				else
					lingwu_jifa=skills_jifaid[lingwu_skill]
				end 
			end
	var["lingwu_jifa"]=lingwu_jifa
	return lingwu_skill,lingwu_jifa,lianxi_weapon,lingwu_mark
end

--触发
add_trigger("lianxi_1","^\\s*(?:.*练.*须空*|.*学.*须空*|空*方能练|空*才能练|空了*方能练|空了*才能练)",function(params)
	var["check_lianxi"]="需要空手练习"
end)
add_trigger("lianxi_2","^\\s*.*必须(?:有|以)(\\S+)(?:配合。|为根基)",function(params)
	var["check_lianxi"]="需要激发内功："..params[1]
end)
add_trigger("lianxi_3","^\\s*你现在的修为不足以提高",function(params)
	--echo("\n"..C.W.."ok")
	var["idle"]=0
	local pot=var["pot"] or 0
	if pot<10 then
		var["check_lianxi"]="潜能不够"
	else
		var["check_lianxi"]="内力不够"
	end
end)

--  你的潜能已经用完了，再怎么读也没用。
--  你现在的修为不足以提高
add_trigger("lianxi_14","^\\s*你的潜能已经用完了，再怎么读也没用。",function(params)
	var["check_lianxi"]="潜能不够"
end)
add_trigger("lianxi_13","^\\s*你的真气不够。",function(params)
	var["check_lianxi"]="内力不够"
end)
add_trigger("lianxi_4","^\\s*你的「.*」进步了！",function(params)
	var["check_lianxi2"]="继续领悟"
end)
--> 你使用的武器不对。
add_trigger("lianxi_5","^\\s*(?: 你手上的武器不能用来练|你必须先找一条鞭子才能练鞭法。|你使用的武器不对。|你必须使用杖来练|你没有使用的武器。|.*空手时无法练|你必须使用金蛇剑才能进一步练习你的金蛇剑法。)",function(params)
	var["check_lianxi"]="需要装备武器"
end)
add_trigger("lianxi_6","^\\s*(?:你的内力不够|你的内力太差|你先聚集点内力再练|你感觉全身气息翻腾，原来你真气不够，不能装备)",function(params)
	var["check_lianxi"]="内力不够"
end)
add_trigger("lianxi_7","^\\s*(?:你不能通过基本招架来练习。|辟邪剑法只能通过研习「葵花宝典」来学习。|独孤九剑只能通过领悟来提高。|你必须通过学习才能练新的一招。|你不散掉.*，如何能修习.*。|你没有出家，不能领会更高深的龙象般若功|你的基本功火候未到，必须先打好基础才能继续提高。|你的内功火候太浅。|由于实战经验不足，阻碍了你的「)",function(params)
	local lingwu_skill,lingwu_jifa=get_lingwu_jifa_skill(var["lingwu_skills"],var["lingwu_count"],var["skills_jifaid"])		
	var["lingwu_jifa"]=lingwu_jifa
	if string.find(line[1],"由于实战经验不足，阻碍了你的「基本内功") and lingwu_skill~="force" then
	
	else
		var["check_lianxi"]="领悟下一个技能"
	end
end)
add_trigger("lianxi_9","^\\s*│等级：\\s+\\S+\\s+(\\d+)/\\s*(\\d+)\\s+类别",function(params)
	local l=tonumber(params[1])
	local weishu=tonumber(params[2])
	l=l+1
		l=l*l
	if weishu>=l then --尾数超了
		var["check_lianxi2"]="尾数练超了"
	end
end)
add_trigger("lianxi_10","^\\s*你的紫霞神功火候不够，无法学习混元掌。",function(params)
	var["check_lianxi"]="需要激发内功：华山气功"
end)
--> 你的紫霞神功火候不够，无法学习混元掌。

--你并非少林弟子，如何习得了少林神功。xx的无法练习易筋经
add_trigger("lianxi_11","^\\s*你并非少林弟子，如何习得了少林神功。",function(params)
	var["check_lianxi"]="只能读书"
end)

add_trigger("lianxi_8","^\\s*你把\\s+\"action\"\\s+设定为\\s+\"技能练习中",function(params)
	local check_lianxi=var["check_lianxi"] or ""
	local check_lianxi2=var["check_lianxi2"] or ""
	local lingwu_skill,lingwu_jifa,lianxi_weapon,lingwu_mark=get_lingwu_jifa_skill(var["lingwu_skills"],var["lingwu_count"],var["skills_jifaid"])
	local neili=var["neili"] or 100
	--echo("\n"..C.W..var["check_lianxi"])
	--echo("\n"..C.W..var["pot"])
	--echo("\n"..C.W..neili)
	--echo("\n"..C.W..pot)
	if check_lianxi2=="继续领悟" then
		close_lianxi()
		close_lingwu()
		unset_timer("timer")
		var["xuexiweapon"]=var["xuexiweapon"] or ""
--		check_busy2(function()
			send("unwield "..var["xuexiweapon"])
			exec("n;n;lingwu_skills")
--		end)
	else
		if string.find(check_lianxi,"需要激发内功") then
			echo("\n"..C.W.."练习内功不对！")
			close_lianxi()
			unset_timer("timer")
			local force_name=string.match(var["check_lianxi"],"需要激发内功：(.+)")
			local skills_id=var["skills_id"] or {}
				if skills_id[force_name] then
					check_busy2(function()
						send("jifa force "..skills_id[force_name])
						exec("lian_skills")
					end)
				else
					exec("n;n;lingwu_skills_next")
				end
		elseif check_lianxi=="需要空手练习" then
			echo("\n"..C.W.."练习要空手！")
			close_lianxi()
			unset_timer("timer")
			exec("unwield_weapon")
			check_busy2(function()				
				for k,v in pairs(var["equipment_s"]) do
					if string.find("|changjian|blade|liuxing chui|tiegun|gangzhang|changbian|hook|zhubang|gang fu|ansha bishou|xiao|mu jian|mu dao|snowsword|youlong bian|zhen|jinshe zhui|zhu bang|yu xiao|bishou|kulou chui|tiegun|dafu|falun|hongying qiang|hanyu gou|lanyu duzhen|xue sui|sangmen ding|panguan bi|yinshe sword|tianshe zhang|coin|tie bi|tiebi|songwen jian|tie jiang|","|"..k.."|") then
						send("unwield "..k)
						send("remove glove")
						send("unwield "..k.." 2")
						--[[send("i")
	local equip=var["equipment_s"] or {}
	for k,v in pairs(equip) do
		send("unwield "..k)
		send("unwield "..k.." 2")
	end]]
					end
				end
				exec("lian_skills")
			end)
		elseif check_lianxi=="需要装备武器" then
			echo("\n"..C.W.."练习要武器！")
			send("remove glove")
			close_lianxi()
			unset_timer("timer")
			check_busy2(function()
				for k,v in pairs(var["equipment_s"]) do
					if string.find("|changjian|blade|liuxing chui|tiegun|gangzhang|changbian|hook|zhubang|gang fu|ansha bishou|xiao|mu jian|mu dao|snowsword|youlong bian|zhen|jinshe zhui|zhu bang|yu xiao|bishou|kulou chui|tiegun|dafu|falun|hongying qiang|hanyu gou|lanyu duzhen|xue sui|sangmen ding|panguan bi|yinshe sword|tianshe zhang|coin|tie bi|tiebi|songwen jian|tie jiang|","|"..k.."|") then
						send("unwield "..k)
						send("remove glove")
						send("unwield "..k.." 2")
					end
				end
				--先卸下武器
				var["xuexiweapon"]=get_skill_weapon_id(var["lingwu_jifa"]) or ""
				if lianxi_weapon~="" and item[lianxi_weapon] then --规定了使用武器，且行囊中有这把武器
					close_lianxi()
					close_lingwu()
					unset_timer("timer")
					check_busy2(function()
						send("wield "..lianxi_weapon)
						exec("lian_skills")
					end)
				
				else
				
					if var["xuexiweapon"]=="" then --没找到武器领悟下一个
						close_lianxi()
						close_lingwu()
						unset_timer("timer")
				--		check_busy2(function()
							exec("n;n;lingwu_skills_next")
				--		end)
					else
						if item and item[var.xuexiweapon] then --行囊有武器
							close_lianxi()
							close_lingwu()
							unset_timer("timer")
							check_busy2(function()
                                send("remove glove")
								send("wield "..var["xuexiweapon"])
								exec("lian_skills")
							end)
						else --行囊无武器要去买
							close_lianxi()
							close_lingwu()
							unset_timer("timer")
							if var["xuexiweapon"]=="hongying qiang" then
								var["qu_gold"]="买红缨白蜡大枪"
							end
							if var["xuexiweapon"]=="yinshe sword" then
								var["qu_gold"]="买银蛇剑"
							end
							if var["xuexiweapon"]=="panguan bi" then
								var["qu_gold"]="买判官笔"
							end
							wait(1,function()
								exec("do_buy "..var["xuexiweapon"])
							end)
						end
					end
				end
			end)			 
		--[[elseif check_lianxi=="只能读书" then
			echo("\n"..C.W.."凯歌个懒猪，不改bug")
			--exec("i")
			--close_lianxi()
			--close_lingwu()
			local fanwenbook=item["fanwen book"] or 0
			--open_fullfanwenbook()
			unset_timer("timer")
			echo("\n"..C.R..fanwenbook)
			wait(1,function()
				exec("full_xx_yjj") --检查身上是不是有书？
			end)]]
		elseif check_lianxi=="潜能不够" then
			echo("\n"..C.W.."练习潜能不够！")
			close_lianxi()
			close_lingwu()
			unset_timer("timer")
			wait(1,function()
				exec("after_usepot")
			end)
		elseif check_lianxi2=="尾数练超了" then
			echo("\n"..C.W.."练习尾数超了！")
			close_lianxi()
			close_lingwu()
			unset_timer("timer")
					var["xuexiweapon"]=var["xuexiweapon"] or ""
					exec("unwield @xuexiweapon;n;n;lingwu_skills_next")
		elseif check_lianxi=="内力不够" or neili<20 then -- or neili<200
			echo("\n"..C.W.."练习内力不够！")
			close_lianxi()
			close_lingwu()
			local dahuandan1=0
			local dahuandan2=0
			local chuanbeiwan=0
			local start_wash=var["start_wash"] or 0
			local usedrug=var["usedahuandan"] or 0--用药
			local usechuanbei=var["chuanbeiwan"] or 0
			local usedahuandan=var["dahuandan"] or 0
			local tongbao=var["tongbao"] or 0 --通宝总数
			local gold=var["gold"] or 0 --存款总数
			if not null(item) then
				dahuandan1=item["da huandan"] or 0
				dahuandan2=item["dahuan dan"] or 0
				chuanbeiwan=item["chuanbei wan"] or 0
				gold1=item["gold"] or 0 --身上的gold
			end
			--echo("\n"..C.W..start_wash)
			--echo("\n"..C.W..gold)
			--echo("\n"..C.W..gold1)
			if usedrug==4 and chuanbeiwan>0 then
				wait(3,function()
					exec("eat chuanbei wan;lian_skills")
				end)
			elseif var["usedahuandan"] and var["usedahuandan"]==4 and dahuandan1>0 and ((item["da huandan"] and item["da huandan"]>0) or (item["dahuan dan"] and item["dahuan dan"]>0)) then
				exec("fu dan;lian_skills")
			elseif var["usedahuandan"] and var["usedahuandan"]==4 and dahuandan2>0 and ((item["da huandan"] and item["da huandan"]>0) or (item["dahuan dan"] and item["dahuan dan"]>0)) then
				exec("fu dan;lian_skills")
			elseif var["usedahuandan"] and var["usedahuandan"]==4 and usechuanbei>4 and chuanbeiwan<1 then --chuanbei wan不够
	--			exec("changejob")
				close_lingwu()
				close_lianxi()
				unset_timer("timer")
				wait(2,function()
					exec("after_usepot")
				end)
			elseif usedrug==4 and usedahuandan>4 and dahuandan1<1 and dahuandan2<1 and tongbao>100 then --大还丹不够
	--			exec("changejob")
				close_lingwu()
				close_lianxi()
				unset_timer("timer")
				wait(2,function()
					exec("after_usepot")
				end)
			elseif start_wash==1 and gold>500 then --设置了洗澡而且存款大于1000锭金子，没钱的穷逼就算了
			    close_lingwu()
				close_lianxi()
				unset_timer("timer")
				wait(2,function()
					exec("i;score;after_wash_lianxi")
				end)	
			else
				var["idle"]=0 --万一红了一直练呢，算了先这样了
				set_dazuo("lianxi")
				open_dazuo()
				check_busy(function()
					exec("do_dazuo")
				end)
			end	
		elseif check_lianxi=="领悟下一个技能" then
			echo("\n"..C.W.."练习领悟下一个技能！")
			close_lianxi()
			close_lingwu()
			unset_timer("timer")
		--	check_busy2(function()
				var["xuexiweapon"]=var["xuexiweapon"] or ""
				exec("unwield @xuexiweapon;n;n;lingwu_skills_next")
		--	end)	

		end
	end
	var["check_lianxi"]=nil
	var["check_lianxi2"]=nil
end)
add_alias("after_wash_lianxi",function(params) --使用潜能
	gold1=item["gold"] or 0 --身上的gold
	if gold1<10 then
		g(653,function() --潜能银行
		 wait(1,function()
			exec("qu 20 gold;i")
			check_busy(function()
				exec("after_wash_lianxi")
			end)
		end)
	end)
	else
	exec("after_wash_start_lianxi")
  end
end)

add_alias("after_wash_start_lianxi",function(params) 
	var["idle"]=0
	--local sex=var["sex"] or ""
	g(687,function() --清池
		wait(1,function()
		      --exec("unwield_weapon")
		send("i")  --检测下武器
	     local equip=var["equipment_s"] or {}
	     for k,v in pairs(equip) do
		send("unwield "..k)
		send("unwield "..k.." 2")
	end
	       if var["sex"]=="女性" then
	       exec("give yahuan 1 gold;e;remove all;wash")
	       else
	       exec("give yahuan 1 gold;w;remove all;wash")
	        end
		check_busy(function() 
			g(686,function() --茶馆
				exec("wear all")
				 exec("lian_skills")
				end)
			end)
		end)
	end)
end)			






add_alias("full_xx_yjj",function(params) --xx易筋经
   -- open_lianxi()
	set_timer("timer",0.2,function()
			exec("read fanwen book;yun jing")
		end)
end)



add_alias("after_xx_yjj",function(params) --检查身上是不是有书
  -- exec("i")
   local fanwenbook=item["fanwen book"] or 0
	echo("\n"..C.R..fanwenbook)  	
	 if fanwenbook==0 then--身上没有书
		var["idle"]=0
		wait(1,function()
			g(3671,function()
				check_busy(function()
					exec("search 草丛")
					   check_busy(function()
						 exec("look xiaobao;open xiaobao;after_xx_yjj")
					end)
				end)
			end)
        end)
      else
	  exec("full_xx_yjj") --准备好了，开始吧
end
end)

--[[add_trigger("full_skill_20","\^\\s*你把\\s+\"action\"\\s+设定为\\s+\"易筋经读好了么",function(params)
echo("\n"..C.x.."Lua 内存清理前："..collectgarbage("count"))
collectgarbage("collect")
echo(""..C.x.."Lua 内存清理后："..collectgarbage("count"))
    open_lianxi()
	open_lingwu()
exec("#15 read read fanwen book;yun jing;alias action 易筋经读好了么...")
end)
g(561,function()
	exec("alias action 易筋经读好了么读好了么...")
		end)

end)]]


function open_lianxi()
	open_trigger("lianxi_1")
	open_trigger("lianxi_2")
	open_trigger("lianxi_3")
	open_trigger("lianxi_4")
	open_trigger("lianxi_5")
	open_trigger("lianxi_6")
	open_trigger("lianxi_7")
	open_trigger("lianxi_8")
	open_trigger("lianxi_9")
	open_trigger("lianxi_10")
	open_trigger("lianxi_11")
	open_trigger("lianxi_12")
	open_trigger("lianxi_13")
	open_trigger("lianxi_14")
end
function close_lianxi()
	close_trigger("lianxi_1")
	close_trigger("lianxi_2")
	close_trigger("lianxi_3")
	close_trigger("lianxi_4")
	close_trigger("lianxi_5")
	close_trigger("lianxi_6")
	close_trigger("lianxi_7")
	close_trigger("lianxi_8")
	close_trigger("lianxi_9")
	close_trigger("lianxi_10")
	close_trigger("lianxi_11")
	close_trigger("lianxi_12")
	close_trigger("lianxi_13")
	close_trigger("lianxi_14")
end
close_lianxi()

Print("--- 加载模块: 领悟 ---")