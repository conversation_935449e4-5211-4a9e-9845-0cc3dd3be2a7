-- ===================================================================
-- prepare.lua 重构版本
-- 日期: 2025-06-30
-- 作者: Cfan
-- 简介: 本文件根据重构计划对原始代码进行了结构优化。
--       主要负责在执行任务前进行一系列的准备工作，
--       包括状态检查、物品管理、装备修理、任务选择等，
--       确保角色处于最佳状态以执行下一个任务。
-- ===================================================================

-- ===================================================================
-- 1. 常量定义
-- 本模块定义了脚本中使用的各种常量，便于维护和修改。
-- ===================================================================

-- 可购买武器的正则表达式模式
-- 用于判断一个武器ID是否对应商店里可以买到的武器。
local BUYABLE_WEAPON_PATTERN =
"^(?:金蛇锥|jinshe zhui|长剑|钢刀|流星锤|铁棍|钢杖|长鞭|单钩|竹棒|钢斧|暗杀匕首|箫|木剑|铁笔|风回雪舞剑|游龙长鞭|暴雨梨花针|仿制玉竹棒|玉箫|百辟匕首|骷髅锤|齐眉棍|宣花大斧|changjian|blade|liuxing chui|tiegun|gangzhang|changbian|hook|zhubang|gang fu|ansha bishou|xiao|mu jian|tie bi|snowsword|youlong bian|zhen|zhu bang|yu xiao|bishou|kulou chui|tiegun|dafu|红缨白蜡大枪|秘传法轮|寒玉钩|蓝玉毒针|碎雪刀|丧门钉|判官笔|天蛇杖|银蛇剑|阴阳九龙令|松纹剑|hongying qiang|falun|hanyu gou|lanyu duzhen|xue sui|sangmen ding|panguan bi|yinshe sword|tianshe zhang|jiulong ling|songwen jian)"

-- 任务全名到简称的映射表
-- 用于在记录上一个任务时，将任务全名转换为易于处理的简称。
local JOB_NAME_MAPPING = { ["大理送信"] = "sx", ["武当锄奸"] = "wd", ["强抢美女"] = "xs", ["少林护送"] = "slhs", ["七窍玲珑"] = "qqll",
    ["明教巡逻"] = "xl", ["惩恶扬善"] = "hs", ["长乐帮"] = "clb", ["丐帮"] = "gb", ["嵩山并派"] = "ss", ["官府捕快"] = "gf", ["刺杀敌元帅"] = "smy",
    ["火烧草料场"] = "smy", ["抗敌颂摩崖"] = "smy", ["守卫襄阳"] = "swxy", ["丐帮抓蛇"] = "zs", ["天地会"] = "tdh", ["护镖"] = "hb" }

-- 用于 unwield (卸下) 的武器列表
-- 这是一个以 "|" 分隔的字符串，包含了在准备阶段需要明确卸下的武器ID。
local UNWIELD_WEAPON_LIST_STRING =
"|changjian|blade|liuxing chui|tiegun|gangzhang|changbian|hook|zhubang|gang fu|ansha bishou|xiao|mu jian|mu dao|snowsword|youlong bian|zhen|jinshe zhui|zhu bang|yu xiao|bishou|kulou chui|tiegun|dafu|falun|hongying qiang|hanyu gou|lanyu duzhen|xue sui|sangmen ding|panguan bi|yinshe sword|tianshe zhang|coin|tie bi|tiebi|songwen jian|"

-- 需要特殊处理（存入仓库）的物品列表
-- 这是一个映射表，key是物品的ID，value是存入时使用的命令或别名。
local SPECIAL_ITEMS_TO_STORE = { ["ebook"] = "ebook", ["miwen"] = "miwen", ["chanpian"] = "canpian", ["yuehua shi"] =
"yuehua shi", ["shensheng zhufu"] = "shensheng zhufu", ["weilan"] = "韦兰之锤", ["hantie"] = "hantie", ["shentie"] = "万年神铁",
    ["xuantie"] = "玄铁", ["longsi"] = "玉蚕丝", ["tiancan"] = "天蚕丝", ["cansi"] = "蚕丝", ["mumianhua"] = "木棉花", ["longyang"] =
"mizong longyangsan", ["qqlly"] = "yu", ["pearl"] = "pearl", ["mingxin bodhi"] = "mingxin bodhi" }

-- 常用非特殊武器列表（用于购买和清理）
-- 这是一个以 "|" 分隔的字符串，包含了脚本可能会购买或清理的常规武器。
local STANDARD_WEAPON_LIST_STRING =
"|changjian|blade|hook|gang fu|mu jian|liuxing chui|tiegun|changbian|gangzhang|zhubang|xiao|tie bi|songwen jian|lanyu duzhen|"


-- ===================================================================
-- 2. 辅助函数
-- 本模块包含一系列小功能函数，为核心逻辑提供支持。
-- ===================================================================

-- 记录新获得的“田七鲨胆散”数量的全局变量。
new_tianqi = 0

---
-- @function create_canwu_check
-- @brief 创建参悟检查项的通用函数，用于消除重复代码
-- @param canwu_type (string) 参悟类型，如 "canwuexp", "canwugift", "canwustunt", "canwuneili"
-- @param required_exp_var (string) 所需经验变量名，如 "docanwuexp"
-- @param canwu_flag_var (string) 参悟标志变量名，如 "canwuexp"
-- @param quest_action (string) 执行的任务动作，如 "do_quest canwuexp"
-- @param min_exp_threshold (number) 最小经验阈值
-- @param default_flag_value (number) 默认标志值，通常为0或1
-- @param special_action (function) 可选的特殊动作函数
-- @return (table) 包含name、condition和action的检查项
local function create_canwu_check(canwu_type, required_exp_var, canwu_flag_var, quest_action, min_exp_threshold,
                                  default_flag_value, special_action)
    return {
        name = "参悟" .. canwu_type:upper(),
        condition = function()
            local e, d = var["exp"] or 0, var[required_exp_var] or 0
            return (var[canwu_flag_var] or default_flag_value) == 0 and d > 0 and
                ((e > min_exp_threshold and d == 1) or (e > d and d > min_exp_threshold - 1))
        end,
        action = function()
            if special_action then special_action() end
            exec(quest_action)
        end
    }
end

---
-- @function get_fullname
-- @brief 将武器的简称或别名转换为完整的物品ID。
-- @param name (string) 武器的简称，如 "jian"。
-- @return (string) 武器的完整ID，如 "changjian"，如果未找到匹配则返回原名称。
local function get_fullname(name)
    local names = { ["mu jian"] = "mu jian", ["bishou"] = "ansha bishou", ["tiegun"] = "tiegun", ["tie bi"] = "tie bi",
        ["zhubang"] = "zhubang", ["xiao"] = "xiao", ["ansha bishou"] = "ansha bishou", ["gangzhang"] = "gangzhang",
        ["brush"] = "tie bi", ["liuxing chui"] = "liuxing chui", ["coin"] = "coin", ["dagger"] = "ansha bishou", ["dao"] =
    "blade", ["gang fu"] = "gang fu", ["bian"] = "changbian", ["jian"] = "changjian", ["gou"] = "hook", ["bi"] = "tie bi",
        ["hook"] = "hook", ["blade"] = "blade", ["fu"] = "gang fu", ["changbian"] = "changbian", ["axe"] = "gang fu",
        ["changjian"] = "changjian", ["chui"] = "liuxing chui", ["hammer"] = "liuxing chui" }
    return names[name] or name
end

---
-- @function remove_job_from_list
-- @brief 从一个以"|"分隔的任务列表字符串中移除指定的任务。使用string.gsub实现更简洁的逻辑。
-- @param job_list_str (string) 任务列表字符串，如 "sx|wd|xs"。
-- @param job_to_remove (string) 需要移除的任务简称。
-- @return (string) 移除了指定任务后的新列表字符串。
local function remove_job_from_list(job_list_str, job_to_remove)
    if not job_list_str or job_list_str == "" then return "" end
    -- 使用gsub替换，处理开头、中间和结尾的情况
    local result = job_list_str:gsub("^" .. job_to_remove .. "|", "") -- 开头
    result = result:gsub("|" .. job_to_remove .. "|", "|")            -- 中间
    result = result:gsub("|" .. job_to_remove .. "$", "")             -- 结尾
    result = result:gsub("^" .. job_to_remove .. "$", "")             -- 整个字符串
    return result
end

---
-- @function get_next_job_from_list
-- @brief 根据上一个任务，从任务列表中循环选择下一个不重复的任务。
-- @param job_list_str (string) 任务列表字符串，如 "sx|wd|xs"。
-- @param last_job (string) 上一个完成的任务简称。
-- @return (string) 下一个要执行的任务简称，如果列表为空则返回 nil。
local function get_next_job_from_list(job_list_str, last_job)
    if not job_list_str or job_list_str == "" then return nil end
    local job_list_tbl = {}
    local last_job_index = 0
    for job in string.gmatch(job_list_str, "([^|]+)") do table.insert(job_list_tbl, job) end
    if #job_list_tbl == 0 then return nil end
    if #job_list_tbl == 1 then return job_list_tbl[1] end
    -- 智能匹配：如果 last_job 是基础简称（如"sx"），需要在 job_list 中找到对应的带数字版本（如"sx1"、"sx2"）
    for i, job in ipairs(job_list_tbl) do
        if job == last_job then
            last_job_index = i; break;
        elseif string.sub(job, 1, -2) == last_job and string.match(job, "%d$") then
            -- 匹配基础简称，如 "sx1" 匹配 "sx"
            last_job_index = i; break;
        end
    end

    local next_job_index = last_job_index % #job_list_tbl + 1
    if job_list_tbl[next_job_index] == last_job then
        for i = 1, #job_list_tbl do if job_list_tbl[i] ~= last_job then return job_list_tbl[i] end end
    end
    return job_list_tbl[next_job_index]
end

---
-- @function has_any_item
-- @brief 检查玩家的物品栏中是否拥有指定列表中的任意一个物品。使用function.lua中的carryqty函数。
-- @param item_list (table) 一个包含物品ID字符串的列表。
-- @return (boolean) 如果拥有至少一个物品，则返回 true，否则返回 false。
local function has_any_item(item_list)
    for _, item_name in ipairs(item_list) do
        if carryqty(item_name) > 0 then return true end
    end
    return false
end

---
-- @function get_quest_adjusted_job
-- @brief 根据特殊Quest（如独孤九剑、吸星大法）的进度，动态调整将要执行的任务。
--        这是一个高度定制化的逻辑，用于在特定条件下强制执行某些任务以推进Quest。
-- @param current_next_job (string) 当前已选择的下一个任务。
-- @param last_job (string) 上一个任务。
-- @param current_job_list (string) 当前的任务列表。
-- @return (string, string) 调整后的下一个任务和任务列表。
local function get_quest_adjusted_job(current_next_job, last_job, current_job_list)
    local _hs = (var["jobtimes"] and var["jobtimes"]["华山岳不群惩恶扬善任务"]) or 0
    local _gb = (var["jobtimes"] and var["jobtimes"]["丐帮吴长老杀人任务"]) or 0
    local _sx = (var["jobtimes"] and var["jobtimes"]["大理王府送信任务"]) or 0
    local _wd = (var["jobtimes"] and var["jobtimes"]["武当宋远桥杀恶贼任务"]) or 0
    local _s = (var["skills"] and var["skills"]["dugu-jiujian"]) or 0
    local _s2 = (var["skills_level"] and var["skills_level"]["xixing-dafa"]) or 0
    local _t = math.fmod((_hs + _sx + _gb), 50)
    local _t2 = math.fmod((_wd + _hs), 50)
    var["9jian"] = _t
    var["no_cun_pearl"] = 0
    local adjusted_job, adjusted_list = current_next_job, current_job_list
    -- 独孤九剑Quest逻辑
    if _t > 2 and _t < 48 and var["party"] == "华山派" and _s < 1 and var["quest1"] == 1 then
        adjusted_list = "hs1|sx1"
    end
    if _t == 49 and var["party"] == "华山派" and _s < 1 and (adjusted_job ~= "hs1" and adjusted_job ~= "hs2") then
        if (var["quest1"] or 0) == 0 or (var["no_9jian_quest"] and var["no_9jian_quest"] == 1) then
            echo("\n" .. C.m .. '提示：独孤九剑quest任务顺序错误，如需自动调整请设置my-config.txt var["quest1"]=1!!!')
        elseif not string.find(adjusted_list, "clb") then
            adjusted_job = "clb"; adjusted_list = "hs1|clb"
            echo("\n" .. C.m .. '提示：独孤九剑quest任务顺序错误，将自动转换任务为hs1|clb')
        end
    end
    -- 吸星大法Quest逻辑
    if _t2 == 48 and var["judge_xxdf"] == 1 and _s2 < 1 and (adjusted_job == "hs1" or adjusted_job == "hs2") and (var["exp"] or 0) > 5000000 and (var["shen"] or 0) >= 10000 then
        adjusted_job = (last_job ~= "sx") and "sx1" or "clb"
    elseif _t2 == 49 and var["judge_xxdf"] == 1 and _s2 < 1 and (adjusted_job == "hs2" or adjusted_job == "hs1") and (var["shen"] or 0) >= 10000 then
        var["no_cun_pearl"] = 1; adjusted_job = "hs1"
    end
    return adjusted_job, adjusted_list
end

---
-- @function reset_job_state
-- @brief 重置与任务执行相关的状态变量。在每次`changejob`时调用，以确保干净的起始状态。
local function reset_job_state()
    var["reboot"] = 0; del_timer("timer"); var["ttt_root"] = 0; var["battleship_root"] = 0; var["must_be_night"] = nil; var["job_step"] = 0; var["do_stop"] = 0; var["job_npc_name"] = nil; var["job_room"] = nil; var["job_npc_id"] = nil; var["fight"] = 0; var["log_fail_fight"] = nil; var["qu_gold"] = nil; var["job_no_fight"] = nil; var["quest_time"] = nil; var["quest_finish"] = nil; var["quest_list"] = nil; var["ttt_start_time"] = nil; var["ttt_start_level"] = nil; var["qinghuaimeng_start_time"] = nil; var["challenge_start_time"] = nil; var["battleship_start_time"] = nil; var["repair"] =
    ""
end

---
-- @function check_and_prepare_equipment
-- @brief 检查并准备角色的装备。包括卸下所有装备以便检查耐久度，并触发对主、副武器的检查流程。
local function check_and_prepare_equipment()
    if var["no_remove_armor"] and var["no_remove_armor"] == 0 then send("remove all") end
    local armors_list = var["armors_list"] or ""; local no_repair_armor = var["no_repair_armor"] or 0
    for v in string.gmatch(armors_list .. "|", "(.-)|") do
        if no_repair_armor ~= 1 then send("look " .. v) end
    end
    var["repair_weapon"] = 0
    local weapons_to_check = { { name = "myweapon", trigger = "item_4", desc = "主用武器" }, { name = "other_weapon", trigger = "item_41", desc = "备用武器" }, { name = "smy_weapon", trigger = "item_42", desc = "爬山武器" } }
    for _, weapon in ipairs(weapons_to_check) do
        local weapon_id = var[weapon.name] or ""
        if weapon_id ~= "" then
            send("unwield " .. weapon_id); var["repair"] = weapon_id; open_trigger(weapon.trigger); exec("look " ..
            weapon_id .. ";alias action 检查" .. weapon.desc .. "...")
        end
    end
end

---
-- @function run_initial_commands
-- @brief 执行一系列初始命令，用于获取角色当前状态（如hp, score, i等）并设置别名，表明正在准备任务。
local function run_initial_commands()
    open_trigger("prepare_3")
    if (var["ado"] or 0) == 2 then
        exec("alias gogogo look yu;i;cond;wear all;hp " ..
        var["char_id"] .. ";cha;nstory;jifa;hp;score;time -s;fu jinyuan san;alias;jobtimes;alias action 准备任务中..."); exes(
        "gogogo", 2)
    else
        exec("look yu;i;cond;wear all;hp " .. var["char_id"] .. ";cha;nstory;jifa;score;fu jinyuan san;alias;jobtimes"); exes(
        "alias action 准备任务中...", 2)
    end
end

---
-- @function close_all_job_triggers
-- @brief 关闭所有与任务相关的触发器，防止在准备阶段或不同任务之间发生冲突。
local function close_all_job_triggers()
    close_all(); close_qinghuaimeng_triggers(); Close_battleship_triggers(); close_crush(); delete_triggers(
    "otherquest_tongtianta", 1, 14); delete_triggers("otherquest_challenge", 0, 7); del_trigger(
    "otherquest_challenge_1_1")
end

---
-- @function handle_special_item_storage
-- @brief 处理特殊物品的存储。遍历`SPECIAL_ITEMS_TO_STORE`列表，如果发现玩家拥有这些物品，则执行存入操作。
--        对 "yu"（玉）有特殊处理，会尝试读取其属性并记录到文件中。
local function handle_special_item_storage()
    local item_found = false
    for item_id, cun_name in pairs(SPECIAL_ITEMS_TO_STORE) do
        if carryqty(item_id) > 0 then
            exec("do_cun 1 " .. cun_name); item_found = true; break
        end
    end
    if not item_found and (carryqty("longling yu") > 0 or carryqty("fengling yu") > 0) then
        exec("look yu")
        exec("do_cun 1 yu")
        add_trigger("cun_nice_yu", "^[ > ]*一排古篆字写着「(.*)」具体功能", function(params)
            local cun_nice_yu = params[1]; log_message("解密任务时，发现有" .. cun_nice_yu .. "，将要存一个" .. cun_nice_yu .. "。", C.x,
                "<lua>")
            local id = var["id"] or GetVar("id") or ""; if id == "" then return end
            Run("#var _path %syspath()"); local path = GetVar("_path"); Run("#unvar _path")
            var.yu_type = (var.yu_type == "dmg") and "攻玉" or "防玉"; var.yu_all = (var.yu_all > 0) and "yes" or "no"; var.yu_pure = (var.yu_pure > 0) and
            "yes" or "no"
            local content = table.concat(
            { cun_nice_yu, var.yu_type, var.yu_str, var.yu_con, var.yu_dex, var.yu_int, var.yu_all, var.yu_att, var
                .yu_def, var.yu_dmg, var.yu_pure }, ",") .. "\n"
            if tostring(var.yu_str) ~= "nil" and var["niceyu"] == 1 then
                local file = io.open(path .. id .. "/yu.txt", "a"); if file then
                    file:write(content); file:close()
                end; close_trigger("cun_nice_yu")
            end
        end)
    end
end

---
-- @function check_holiday_gift
-- @brief 检查当前日期是否为节假日，并根据条件自动前往武馆领取节日礼物。
--        包含领取成功、失败、已领取等情况的完整处理逻辑。
local function check_holiday_gift()
    local holiday_dates_map = { ["20230101"] = true, ["20240101"] = true, ["20250101"] = true, ["20260101"] = true,
        ["20270101"] = true, ["20280101"] = true, ["20290101"] = true, ["20300101"] = true, ["20310101"] = true,
        ["20320101"] = true, ["20230122"] = true, ["20240210"] = true, ["20250129"] = true, ["20260217"] = true,
        ["20270206"] = true, ["20280126"] = true, ["20290213"] = true, ["20300203"] = true, ["20310123"] = true,
        ["20320211"] = true, ["20230405"] = true, ["20240404"] = true, ["20250404"] = true, ["20260405"] = true,
        ["20270405"] = true, ["20280404"] = true, ["20290404"] = true, ["20300405"] = true, ["20310405"] = true,
        ["20320404"] = true, ["20230501"] = true, ["20240501"] = true, ["20250501"] = true, ["20260501"] = true,
        ["20270501"] = true, ["20280501"] = true, ["20290501"] = true, ["20300501"] = true, ["20310501"] = true,
        ["20320501"] = true, ["20230622"] = true, ["20240610"] = true, ["20250531"] = true, ["20260619"] = true,
        ["20270609"] = true, ["20280528"] = true, ["20290616"] = true, ["20300605"] = true, ["20310624"] = true,
        ["20320612"] = true, ["20230929"] = true, ["20240917"] = true, ["20251006"] = true, ["20260925"] = true,
        ["20270915"] = true, ["20281003"] = true, ["20290922"] = true, ["20300912"] = true, ["20311001"] = true,
        ["20320919"] = true, ["20231001"] = true, ["20241001"] = true, ["20251001"] = true, ["20261001"] = true,
        ["20271001"] = true, ["20281001"] = true, ["20291001"] = true, ["20301001"] = true, ["20311001"] = true,
        ["20321001"] = true }
    local today = os.date("%Y%m%d"); local is_holiday = holiday_dates_map[today]
    if is_holiday and (var["holiday_auto"] or "") == 1 and (var["exp"] or 0) > 10000000 and today ~= (var["last_holiday_gift_date"] or "") then
        local function handle_gift_result(log_msg)
            del_timer("holiday_gift_timer"); var["holiday_auto"] = 0; var["last_holiday_gift_date"] = today; exec(
            "changejob"); log_message("[节日礼物]：" .. log_msg, C.C, "<解密>")
        end
        add_trigger("get_lihe_1", "^武馆门卫说道：「闯荡情怀，你也辛苦啦，这个节日礼盒，也许对你有用……」$",
            function()
                exec("open lihe"); handle_gift_result("成功领取节日礼盒！")
            end)
        add_trigger("get_lihe_2", "^你忽然想起自己是死大米，顿时万念俱灰。$", function() handle_gift_result("领取失败，死大米！") end)
        add_trigger("get_lihe_3", "^你已经领取了%*礼包，明年再来吧！$", function() handle_gift_result("今天已经领取过了。") end)
        add_trigger("get_lihe_4", "^武馆门卫说道：「好好努力哦~~~~~~下个节日给你一个大礼包~~~~~~~~」$",
            function() handle_gift_result("领取失败，经验或会员等级不够。") end)
        add_trigger("get_lihe_5", "^武馆门卫说道：「(.*)的经验不够，或者会员等级不够，或者已经领取过节日礼物了！」$",
            function() handle_gift_result("领取失败，经验或会员等级不够或已领取。") end)
        g(257,
            function() set_timer("holiday_gift_timer", 2,
                function()
                    exec("ask menwei about 节日礼物"); log_message("尝试领取...", C.W, "[节日礼物]")
                end)
            end)
    else
        del_timer("holiday_gift_timer"); delete_triggers("get_lihe", 1, 5)
    end
end

-- 通用的副本任务执行函数
local function executeChallenge(taskId, taskName, commandName, startHour, endHour)
    --local fixedTimestamp = getTriggerTimestamp(var["char_id"], taskId, startHour, endHour)
    --local formattedTime = os.date("%Y-%m-%d %H:%M:%S", fixedTimestamp)
    --log_message(var["char_name"] .. "，您好！", C.G, "<lua>")
    --log_message("您今日的 " .. taskName .. " 任务专属开启时间为: " .. formattedTime, C.G, "<解密>")
    exec(commandName)
end
-- ===================================================================
-- 3. 核心准备逻辑 (数据驱动)
-- 本模块使用数据驱动的方式，定义了一系列准备工作的检查项。
-- `prepare_job`函数会按顺序检查这些项，并执行第一个满足条件的动作。
-- ===================================================================

-- 存储所有准备工作的检查列表。
local PREPARATION_CHECKS

---
-- @function initialize_preparation_checks
-- @brief 初始化 `PREPARATION_CHECKS` 表。
--        每个检查项包含：
--        - name: (string) 检查项的名称，用于调试。
--        - condition: (function) 返回布尔值的函数，判断是否需要执行此项。
--        - action: (function) 如果条件满足，则执行此函数中的操作。
local function initialize_preparation_checks()
    PREPARATION_CHECKS = {
        { name = "处理镣铐", condition = function() return carryqty("liao kao") ~= 0 end, action = function() exec(
            "do_quest tiaoshui") end },
        { name = "拿绳子", condition = function() return (var["exp"] or 0) > 2000000 and var["time_mark_shengzi"] == nil and
            carryqty("sheng zi") < 1 and
            (var["shengzi"] == nil or var["shengzi"] == "na" or var["shengzi"] == "na2" or var["shengzi"] == "na3") end, action = function()
            exec("do_quest getshengzi") end },
        { name = "武馆 (极低经验)", condition = function() return (var["exp"] or 0) < 3000 end, action = function()
            require "wuguan"; exec("wg")
        end },
        {
            name = "寻城 (低经验)",
            condition = function() return (var["exp"] or 0) < 150000 end,
            action = function()
                if not var["flag_map_hanshui"] and ((var["skills_jifalevel"] and var["skills_jifalevel"]["dodge"]) or 0) < 90 then
                    add_map("汉水坐船") end
                if var["xc_cunpot"] == 1 and (var["pot"] or 0) > (var["maxpot"] or 100) - 5 and tostring(var["no_cun_pot"] or "no") == "no" then
                    exec("do_cun " .. (var["pot"] or 0) .. " pot")
                elseif (var["usepot"] or 0) == 1 and (var["usepotnum"] or 0) == 0 and (var["pot"] or 0) > (var["maxpot"] or 100) - 50 then
                    exec("usepot_xuexi")
                elseif (var["usepot"] or 0) == 1 and (var["usepotnum"] or 0) == 1 and (var["pot"] or 0) >= (var["maxpot"] or 100) then
                    exec("usepot_xuexi")
                elseif (var["usepot"] or 0) == 1 and (var["usepotnum"] or 0) > 1 and (var["pot"] or 0) > (var["usepotnum"] or 0) then
                    exec("usepot_xuexi")
                elseif tostring(var["no_cun_money"] or "no") == "no" and (item["silver"] or 0) > 300 then
                    exec("do_cun 300 silver")
                else
                    require "xuncheng"; exec("set_job_xc;job_ask")
                end
            end
        },
        { name = "读凌波微步", condition = function() return (var["skills_level"]["lingbo-weibu"] or 0) < 179 and
            carryqty("bojuan") > 0 end, action = function() exec("full_lbwb") end },
        { name = "丢弃帛卷", condition = function() return (var["skills_level"]["lingbo-weibu"] or 0) > 179 and
            carryqty("bojuan") > 0 end, action = function() exec("drop bo juan;drop bojuan;changejob") end },
        { name = "倚天屠龙Quest", condition = function() return var["quest_yttl"] ~= nil end, action = function()
            var["quest_yttl"] = nil; exec("doyttl")
        end },
        {
            name = "处理剧毒",
            condition = function() return (var["cond"] or 0) == 2 end,
            action = function()
                if (var["poison_super_star"] or 0) == 1 then
                    del_trigger("check_poison"); check_busy(function() check_place(function()
                            exec("yun liaodu " .. var["char_id"] .. ";yun qudu " .. var["char_id"]); check_busy(function()
                                exec("changejob") end)
                        end) end)
                else
                    if carryqty("田七鲨胆散") > 0 then check_busy(function() exec("eat tianqi;i;changejob") end) else exec(
                        "liaodu") end
                end
            end
        },
        { name = "处理断手", condition = function() return (var["duanshou"] or 0) ~= 0 end, action = function() wait(120,
                function() exec("do_quit duanshou") end) end },
        { name = "处理轻微中毒", condition = function() return (var["cond"] or 0) == 1 end, action = function() check_place(function()
                exec("check_cond changejob") end) end },
        { name = "主武器丢失且无法购买", condition = function() return (var["useweapon"] or 0) == 1 and carryqty("myweapon") == 0 and
            (var["buymyweapon"] or 0) == 0 end, action = function()
            exec("do_quit weapon"); do_log("logweapon")
        end },
        { name = "购买主武器", condition = function()
            local w = var["myweapon"] or ""; return (var["useweapon"] or 0) == 1 and carryqty("myweapon") == 0 and
            (var["buymyweapon"] or 0) == 1 and var["job"] ~= "xc" and
            (w ~= "mu dao" or (w == "mu dao" and carryqty("mu dao") < 5))
        end, action = function() exec("do_buy " .. var["myweapon"]) end },
        { name = "购买备用武器", condition = function() return (var["useweapon"] or 0) == 1 and var["other_weapon"] and
            var["other_weapon"] ~= "" and (var["buyotherweapon"] or 0) == 1 end, action = function() exec("do_buy " ..
            var["other_weapon"]) end },
        { name = "SMY武器丢失且无法购买", condition = function() return (var["jobsmy_useweapon"] or 0) == 1 and
            carryqty("smy_weapon") == 0 and (var["buysmyweapon"] or 0) == 0 end, action = function()
            exec("do_quit weapon"); do_log("logweapon")
        end },
        { name = "购买SMY武器", condition = function() return (var["jobsmy_useweapon"] or 0) == 1 and
            carryqty("smy_weapon") == 0 and (var["buysmyweapon"] or 0) == 1 end, action = function() exec("do_buy " ..
            var["smy_weapon"]) end },
        { name = "购买衣服", condition = function() return (var["buycloth"] or 0) == 1 end, action = function()
            exec("do_buy_cloth"); var["buycloth"] = nil
        end },
        { name = "取金", condition = function() return carryqty("gold") < 1 and var["job"] ~= "xc" end, action = function()
            exec("do_qu 4 gold") end },
        { name = "疗精", condition = function() return (var["hurtjing"] or 100) < 85 and var["job"] ~= "xc" end, action = function()
            exec("do_healjing prepare") end },
        { name = "疗伤", condition = function() return (var["hurtqi"] or 100) < 98 and var["job"] ~= "xc" and
            var["no_need_heal_qi"] == nil end, action = function() exec("do_healqi") end },
        { name = "购买砍路武器", condition = function() return carryqty("weapon") < 1 and var["job"] ~= "xc" end, action = function() if string.find(STANDARD_WEAPON_LIST_STRING, "|" .. (var["weapon"] or "") .. "|") then
                exec("do_buy " .. var["weapon"]) else
                var["weapon"] = "mu jian"; exec("do_buy mu jian")
            end end },
        { name = "修理装备", condition = function() return (var["repair_weapon"] or 0) > 0 end, action = function() exec(
            "do_repair") end },
        { name = "随机买金元散", condition = function() return carryqty("jinyuan san") == 0 and math.random(6) == 3 and
            var["job"] ~= "xc" end, action = function() exec("do_buy jinyuan san") end },
        { name = "买川贝丸", condition = function() return (var["chuanbeiwan"] or 0) > 0 and
            carryqty("chuanbei wan") < var["chuanbeiwan"] and var["job"] ~= "xc" end, action = function() if carryqty("gold") < 3 then
                exec("do_qu 4 gold") else exec("do_buy chuanbei wan") end end },
        { name = "买火折", condition = function() return carryqty("fire") == 0 and
            ((var["usefire"] or 0) == 1 or (var["fire"] or 0) == 1) and var["job"] ~= "xc" end, action = function() exec(
            "do_buy fire") end },
        { name = "买雄黄", condition = function() return carryqty("xiong huang") == 0 and (var["xionghuang"] or 0) == 1 and
            var["job"] ~= "xc" end, action = function() exec("do_buy xiong huang") end },
        { name = "买大还丹", condition = function() return (var["tongbao"] or 0) > 100 and (var["dahuandan"] or 0) > 0 and
            carryqty("dahuan dan") < var["dahuandan"] end, action = function() exec("do_buy dahuan dan") end },
        { name = "买冰蟾", condition = function() return (var["tongbao"] or 0) > 500 and var["bingchan"] and
            var["bingchan"] > 0 and carryqty("bingchan") < var["bingchan"] and var["job"] ~= "xc" end, action = function()
            exec("do_buy bingchan") end },
        { name = "存田七", condition = function()
            local k = var["kucun"] or {}; return (k["田七鲨胆散"] or 0) < 10 and carryqty("田七鲨胆散") > 0 and new_tianqi > 0
        end, action = function()
            log_message("【" .. var["char_name"] .. "】保存物品【田七鲨胆散】", C.g, "<解密>"); exec("do_cun 1 tianqi")
        end },
        { name = "取田七", condition = function()
            local k = var["kucun"] or {}; return (k["田七鲨胆散"] or 0) > 6 and carryqty("田七鲨胆散") < 1
        end, action = function()
            log_message("【" .. var["char_name"] .. "】提取物品【田七鲨胆散】", C.g, "<解密>"); exec("do_get_tianqi")
        end },
        { name = "买珍珠", condition = function() return (var["tongbao"] or 0) > 200 and (var["need_pearl"] or 0) == 1 and
            carryqty("pearl") < 1 end, action = function() exec("do_get_pearl") end },
        { name = "挑战情怀岛", condition = function() return var["battleship"] == 1 and (var["exp"] or 0) > 100000000 and
                    isMyTimeReady(var["char_id"], "battleship", var["battleship_start_time"],1,19) end,
            action = function() executeChallenge("battleship", "情怀岛", "otherquest_battleship", 1, 19) end },
        { name = "挑战情怀梦", condition = function() return var["auto_qhm"] == 1 and (var["exp"] or 0) > 50000000 and
                    isMyTimeReady(var["char_id"], "qinghuaimeng", var["qinghuaimeng_start_time"],2,20) end,
            action = function() executeChallenge("qinghuaimeng", "情怀梦", "otherquest_qinghuaimeng", 2, 20) end },
        { name = "自动Quest (有珍珠)", condition = function() return var["questpearl"] == 1 and carryqty("pearl") > 0 and
            (var["autoquest"] or 0) == 1 and (var["doquest"] or 0) == 1 and var["job"] ~= "xc" end, action = function()
            exec("autoquest") end },
        { name = "挑战宗师塔",condition = function() return var["auto_challenge"] == 1 and (var["exp"] or 0) > 50000000 and
                    isMyTimeReady(var["char_id"], "challenge", var["challenge_start_time"],2,20) end,
            action = function() executeChallenge("challenge", "宗师塔", "otherquest_challenge", 2, 20) end },
        { name = "挑战通天塔", condition = function() return var["ttt_tz_1"] == 1 and (var["exp"] or 0) > 10000000 and
                    isMyTimeReady(var["char_id"], "ttt", var["ttt_start_time"],1,16) end,
            action = function() executeChallenge("ttt", "通天塔", "otherquest_tongtianta", 1, 16) end },
        { name = "自动Quest (无珍珠)", condition = function() return (var["questpearl"] == nil or var["questpearl"] == 0) and
            (var["autoquest"] or 0) == 1 and (var["doquest"] or 0) == 1 and var["job"] ~= "xc" end, action = function()
            exec("autoquest") end },
        { name = "买新人护腕", condition = function() return carryqty("hu wan") == 0 and (var["exp"] or 0) < 10000000 end, action = function()
            exec("do_buy hu wan") end },
        { name = "吃喝", condition = function() return (var["noeat"] or 0) == 0 and
            math.min(var["food"] or 0, var["water"] or 0) < 50 end, action = function() exec("do_quest eat") end },
        { name = "开启萧府地图", condition = function() return (var["mapxiaofu"] or 0) == 0 and carryqty("jin chai") > 0 end, action = function()
            exec("do_quest xiaofu") end },
        { name = "开启五毒教地图", condition = function()
            local e = var["exp"] or 0; return (var["close_wudujiao"] or 1) == 0 and e > 800000 and
            ((var["mapwudujiao"] or 0) == 0 or ((var["mapwudujiao"] or 0) == 1 and (var["fail_xuezhu"] or "try") == "try")) and
            (var["fail_xuezhu"] or "try") ~= "fail"
        end, action = function() exec("do_quest wudujiao") end },
        { name = "存银子", condition = function() return tostring(var["no_cun_money"] or "no") == "no" and
            carryqty("silver") > 300 end, action = function() exec("do_cun 300 silver") end },
        {
            name = "神龙教大力术处理",
            condition = function() return (var["party"] or "") == "神龙教" and (var["shenlongjiaodalishu"] or 0) ~= 2 end,
            action = function()
                exec("jueji")
                add_trigger("shenlongjiaodalishu_1", "你目前还没有掌握任何绝技。",
                    function()
                        del_trigger("shenlongjiaodalishu_1"); var["shenlongjiaodalishu"] = 2; exec("changejob")
                    end)
                add_trigger("shenlongjiaodalishu_3", "神龙教(\\S+)", function(params)
                    del_trigger("shenlongjiaodalishu_3"); var["shenlongjiaodalishu"] = 2
                    if string.find(params[1], "大力术") then
                        g(2516,
                            function()
                                echo("\n" ..
                                C.c ..
                                "<解密>:" .. os.date("%X") .. C.G .. "【玩家:" .. (var["char_id"] or "") .. "】：重新登陆找老洪要大力丸"); exec(
                                "ask hong antong about 豹胎易筋丸"); wait1(5,
                                    function() exec("eat baotaiyijinwan;changejob") end)
                            end)
                    else
                        exec("changejob")
                    end
                end)
            end
        },
        { name = "开金盒", condition = function() return carryqty("jin he") > 0 and var["job"] ~= "xc" end, action = function()
            exec("do_quest jinhe") end },
        { name = "卖玉", condition = function()
            for k in pairs(item) do if string.find(k, "%syu$") or string.find(k, "lvyu sui") then return tostring(var
                    ["no_cun_item"] or "no") == "no" and (var["niceyu"] or 0) == 0 end end; return false
        end, action = function() exec("do_sell yu") end },
        {
           name = "存特殊物品",
           condition = function()
               if tostring(var["no_cun_item"] or "no") ~= "no" then return false end
               for id, _ in pairs(SPECIAL_ITEMS_TO_STORE) do if (item[id] or 0) > 0 then return true end end
               for k in pairs(item) do if string.find(k, "%syu$") or string.find(k, "lvyu sui") or string.find(k, "密宗龙阳散") or string.find(k, "万年寒冰铁") then return true end end
               return false
           end,
           action = handle_special_item_storage
        },
        { name = "处理密函", condition = function() return has_any_item({ "xiangyang mihan", "menggu mihan", "蒙古秘函", "襄阳秘函" }) end, action = function() if has_any_item({ "xiangyang mihan", "襄阳秘函" }) then
                exec("do_quest xymihan") else exec("do_quest mgmihan") end end },
        { name = "存银票", condition = function()
            for k, v in pairs(item) do if string.find(k, "壹仟两银票") then return tostring(var["no_cun_money"] or "no") ==
                    "no" and v > 0 end end; return false
        end, action = function() for k, v in pairs(item) do if string.find(k, "壹仟两银票") then
                    exec("do_cun " .. v .. " thousand-cash"); break
                end end end },
        { name = "存绳子", condition = function() return carryqty("sheng zi") > 0 and
            (not var["shengzi"] or var["shengzi"] == "cun") end, action = function() exec("do_quest cunshengzi") end },
        { name = "存小树枝", condition = function() return carryqty("xiao shuzhi") > 0 end, action = function() exec(
            "do_quest cunxiaoshuzhi") end },
        -- 使用通用参悟检查函数，减少重复代码
        create_canwu_check("exp", "docanwuexp", "canwuexp", "do_quest canwuexp", 15000000, 1),
        create_canwu_check("gift", "docanwugift", "canwugift", "do_quest canwugift", 24200000, 1),
        create_canwu_check("stunt", "docanwustunt", "canwustunt", "do_quest canwustunt", 24999999, 0,
            function() var["docanwustunt_1"] = 1 end),
        create_canwu_check("neili", "docanwuneili", "canwuneili", "do_quest canwuneili", 100000000, 1),
        { name = "使用潜能", condition = function()
            local u, p, m, n = var["usepot"] or 0, var["pot"] or 0, var["maxpot"] or 100, var["usepotnum"] or 0; return u ==
            1 and ((n == 0 and p > m - 50) or (n == 1 and p >= m) or (n > 1 and p > n))
        end, action = function() exec("usepot_xuexi") end },
        { name = "存潜能", condition = function() return (var["usepot"] or 0) == 0 and (var["noeat"] or 0) == 1 and
            (var["pot"] or 0) > (var["maxpot"] or 100) - 5 and tostring(var["no_cun_pot"] or "no") == "no" end, action = function()
            exec("do_cun " .. (var["pot"] or 0) .. " pot") end },
        { name = "存黄金", condition = function() return tostring(var["no_cun_money"] or "no") == "no" and
            carryqty("gold") > 5 end, action = function()
            local g = carryqty("gold") - 2; exec("do_cun " .. g .. " gold")
        end },
        { name = "打坐恢复内力", condition = function()
            local n, m, r = var["neili"] or 100, var["maxneili"] or 100, var["neili_request"] or 190; return math.min(
            r * m / 100, 2 * m) - 10 - n > 0 and var["job"] ~= "xc"
        end, action = function()
            set_dazuo("prepare"); exec("go_dazuo")
        end },
        { name = "面壁思过(降杀气)", condition = function() return (var["crazy_job"] or 0) > 0 and (var["shen"] or 0) < 1 and
            var["job"] and (var["job"] == "wd" or var["job"] == "hs1" or var["job"] == "hs2") end, action = function()
            exec("do_quest mianbi") end },
        { name = "找巫婆(升杀气)", condition = function() return (var["crazy_job"] or 0) > 0 and (var["shen"] or 0) > -1 and
            var["job"] and (var["job"] == "xs" or var["job"] == "ss") end, action = function() exec("do_quest wupo") end },
        { name = "铁掌山闹鬼处理", condition = function() return var["maptiezhang"] == nil or var["maptiezhang"] == 0 end, action = function()
            exec("find_npc tiezhang") end },
    }
end

---
-- @function prepare_job
-- @brief 核心准备工作函数。
--        这是准备流程的总入口。它会先进行一些时间相关的检查和变量初始化，
--        然后遍历 `PREPARATION_CHECKS` 列表，执行第一个满足条件的准备动作。
--        如果所有准备工作都已完成，则最终开始执行选定的任务。
function prepare_job()
    -- 首次调用时初始化检查列表
    if not PREPARATION_CHECKS then initialize_preparation_checks() end

    -- 检查并重置一些有时间限制的状态/冷却
    if var["time_mark_xuexi_over"] and (var["maxpot"] or 100) < 322 and (var["time_mark_xuexi_over"] + 7200) < os.time() then
        var["xuemasterover"] = 0; var["xueplayerover"] = 0; var["time_mark_xuexi_over"] = nil; do_log("xuexiagain");
    end
    if (var["lian_force"] or 0) == 2 and (var["jifa_force"] or "") ~= "" and (var["lianforceoverpot"] or 0) < (var["maxpot"] or 100) and ((var["skills_level"] and var["skills_level"][var["jifa_force"]]) or 0) < (var["maxpot"] or 100) - 100 then var["lian_force"] = 1; end
    if var["time_mark_wudujiao_xuezhu"] and (var["fail_xuezhu"] or "try") == "fail" and (var["time_mark_wudujiao_xuezhu"] + 900) < os.time() then
        var["fail_xuezhu"] = "try"; var["time_mark_wudujiao_xuezhu"] = nil;
    end
    if var["time_mark_shengzi"] and (var["shengzi"] == nil or var["shengzi"] == "na" or var["shengzi"] == "na2" or var["shengzi"] == "na3") and (var["time_mark_shengzi"] + 900) < os.time() then var["time_mark_shengzi"] = nil; end
    if var["time_mark_shuzhi"] and (var["xiaoshuzhi"] == nil or var["xiaoshuzhi"] == "na" or var["xiaoshuzhi"] == "na2" or var["xiaoshuzhi"] == "na3") and (var["time_mark_shuzhi"] + 900) < os.time() then var["time_mark_shuzhi"] = nil; end
    if var["time_mark_liaobed"] and ((var["skills_level"] and var["skills_level"]["yunu-xinjing"]) or 0) > 40 and (var["party"] or "") == "古墓派" and (var["time_mark_liaobed"] + 205) < os.time() then var["time_mark_liaobed"] = nil; end

    -- 初始化各种副本/挑战的冷却时间变量
    if var["battleship_start_time"] == nil then
        var["battleship_start_time"] = 1; send("alias 挑战情怀岛 " .. var["battleship_start_time"]);
    end
    if var["qinghuaimeng_start_time"] == nil then
        var["qinghuaimeng_start_time"] = 1; send("alias 挑战情怀梦 " .. var["qinghuaimeng_start_time"]);
    end
    if var["quest_time"] == nil then
        var["quest_time"] = 1; send("alias QUESTtime " .. var["quest_time"]);
    end
    if var["ttt_start_time"] == nil then
        var["ttt_start_time"] = 1; send("alias 挑战通天塔 " .. var["ttt_start_time"]);
    end
    if var["ttt_start_level"] == nil then
        var["ttt_start_level"] = 1; send("alias 通天塔楼层起点 " .. var["ttt_start_level"]);
    end
    if var["challenge_start_time"] == nil then
        var["challenge_start_time"] = 1; send("alias 挑战宗师塔 " .. var["challenge_start_time"]);
    end

    -- 判断是否需要自动做Quest
    var["autoquest"] = 0
    if (var["exp"] or 0) > 2000000 and os.time() > (var["quest_time"] or 0) and (var["doquest"] or 0) == 1 then
        var["need_pearl"] = (var["questpearl"] == 1) and 1 or 0; var["autoquest"] = 1
    end

    -- 检查是否可以领节日礼物
    check_holiday_gift()
    fangqi_skills_main()
    check_busy2(function()        
        -- 遍历所有准备检查项
        for _, check in ipairs(PREPARATION_CHECKS) do
            if check.condition() then
                if (var["debug"] or 0) > 0 then log_message("执行准备步骤: " .. check.name, C.x, "<lua>") end
                check.action(); return -- 满足一个条件并执行后，立即返回，等待下一次`changejob`调用
            end
        end

        -- 如果所有检查都通过了，说明准备完成
        if (var["debug"] or 0) > 1 then log_message("你仔细核对无误，放心开始任务。", C.x, "<lua>") end
        check_idle(); local n = collectgarbage("count"); collectgarbage("collect"); log_message(
        "LUA内存清理：" .. C.c .. n .. C.x .. " --> " .. C.y .. collectgarbage("count"), C.x, "<lua>");

        -- 设置并开始执行任务
        set_job(var["job"]);
        exec("wield_weapon;job_ask")
    end)
end

-- ===================================================================
-- 4. 主函数与别名
-- 本模块定义了启动准备流程的入口别名和关键的触发器。
-- ===================================================================

---
-- @trigger online_time_check
-- @brief 检查在线时间，如果超过1小时则设置铁掌标记和拿绳子标记
add_trigger("online_time_check", "^[ > ]*您已经连续玩了(.*)。", function(params)
    del_trigger("online_time_check")
    local total_hour = convert_chinese_time(params[1], "hours")
    if total_hour and total_hour > 1 then
        var["maptiezhang"] = 1
        var["time_mark_shengzi"] = os.time()         
    end
end)

---
-- @trigger get_tianqi
-- @brief 捕获获得“田七鲨胆散”的系统消息，并增加计数器。
add_trigger("get_tianqi", "^[ > ]*行走江湖，一包田七鲨胆散怎么能不常备应急呢？你被赠送了一包田七鲨胆散！", function()
    new_tianqi = new_tianqi + 1
end)

---
-- @alias changejob
-- @brief 核心别名，用于在任务完成、失败或需要切换时调用，启动新一轮的准备和任务选择流程。
add_alias("changejob", function(params)
    if var["reboot"] == 1 then exec("reload_all_modules") end
    var["previous_job_code"] = var["job"]
    close_all_job_triggers(); send("set wimpy 100"); open_trigger("other_qinghuai_jueji"); reset_job_state()
    if math.random(20) == 10 then send("save") end

    -- 等待一段时间以确保之前的动作完成，然后开始准备工作
    local wait_time = (var["usearmor"] or 0) ~= 0 and 35 or 16
    wait1(wait_time, function()
        check_and_prepare_equipment(); run_initial_commands()
    end)

    -- 特殊事件触发器：天山灯火
    add_trigger("tianshan_denghuo_1", "你正在路上走着，忽见右首山谷中露出一点", function(params)
        log_message("看到天山灯火。看到灯火提示表示第一次判断成功。第二次判断救童姥，要求per<15，成功R(kar)>=20,R(pur)>=25,R(41-per)>=25", C.C, "<解密>")
    end)
    exec("jueji") -- 检查绝技状态
end)

---
-- @trigger prepare_3
-- @brief 在`run_initial_commands`之后被触发，是任务选择和最终准备流程的核心。
--        它负责清理背包、选择下一个任务，并最终调用`prepare_job`。
add_trigger("prepare_3", "^[ > ]*你把\\s+\"action\"\\s+设定为\\s+\"准备任务中...", function(params)
    del_timer("input"); close_trigger("prepare_3")

    -- 清理背包中的杂物和多余装备
    local equip = var["equipment_s"] or {}
    for k, v in pairs(equip) do
        if string.find(UNWIELD_WEAPON_LIST_STRING, "|" .. k .. "|") then send("unwield " .. k .. " 2") end
    end
    if equip["tie jiang"] then send("unwield tie jiang") end
    if carryqty("xiuhua zhen") > 0 then send("drop xiuhua zhen") end
    if carryqty("xue sui") > 2 and var["roomname"] and var["roomname"] ~= "正厅" then send("drop xue sui") end
    if var["no_get_shengzi"] and var["no_get_sengzi"] == 1 and carryqty("xiao shuzhi") > 0 then
        send("drop xiao shuzhi"); item["xiao shuzhi"] = 0;
    end
    if var["no_get_shengzi"] and var["no_get_sengzi"] == 1 and carryqty("sheng zi") > 0 then
        send("drop sheng zi"); item["sheng zi"] = 0;
    end
    exec("unwield tiechui;wield_weapon"); var["need_pearl"] = 0

    -- 根据任务列表和各种状态选择下一个任务
    -- 优先使用 status.lua 中获取的中文任务名，通过映射表转换为基础简称
    local chinese_lastjob = var["lastjob"] or "无"
    local lastjob = JOB_NAME_MAPPING[chinese_lastjob] or (var["previous_job_code"] or "sx")
    --[[
    if (var["debug"] or 0) > 0 then
        log_message("任务转换: 中文[" .. chinese_lastjob .. "] -> 简称[" .. lastjob .. "]", C.x, "<lua>")
    end
    --]]
    if string.find(var["job_list"], "&") then
        var["job_list2"] = string.match(var["job_list"], ".*&(.*)")
        var["job_list"] = string.match(var["job_list"], "(.*)&.*")
    end
    local job_list_str = var["job_list"] .. "|" .. var["job_list"]; local job_list1 = job_list_str; local job_list2_str =
    var["job_list2"] .. "|" .. var["job_list2"]

    -- 处理特殊任务的冷却时间（如SMY, SWXY）
    if var["time_mark_smy"] and var["time_mark_smy"] + 600 < os.time() then
        if (var["smy_jobbusy"] or 0) < 1 then var["time_mark_smy"] = nil end
    elseif var["time_mark_smy"] then
        log_message("颂摩崖任务剩余： " .. C.Y .. (600 + var["time_mark_smy"] - os.time()) .. C.W .. " 秒。", C.W, "<lua>")
    end
    if (string.find(job_list_str, "smy") and (var["exp"] or 0) > 1000000 and (var["smy"] or 0) < 4 and var["time_mark_smy"] == nil) or var["time_mark_smy"] ~= nil or ((var["smy_jobbusy"] or 0) > 30) then
        job_list_str = remove_job_from_list(job_list_str, "smy")
    end
    if var["jobslhs_fail_wait"] ~= nil and string.find(job_list_str, "slhs") then
        job_list_str = remove_job_from_list(job_list_str, "slhs")
    end
    job_list1 = job_list_str
    if var["time_mark_swxy"] and var["time_mark_swxy"] + 600 < os.time() then
        if (var["swxy_jobbusy"] or 0) < 1 then var["time_mark_swxy"] = nil end
    elseif var["time_mark_swxy"] then
        log_message("守卫襄阳任务剩余： " .. C.Y .. (600 + var["time_mark_swxy"] - os.time()) .. C.W .. " 秒。", C.W, "<lua>")
    end

    -- 根据任务失败、繁忙等状态切换任务列表
    if var["job_fail_wait"] ~= nil or (var["fangqi_job"] or "none") ~= "none" then
        job_list_str = job_list2_str; var["fangqi_job"] = nil;
    end
    if var["guanfu_reset"] ~= nil then job_list_str = job_list2_str; end
    if ((var["wudang_jobbusy"] or 31) < 31 and string.find(var["job_list"], "wd")) or ((var["xueshan_jobbusy"] or 31) < 31 and string.find(var["job_list"], "xs")) then job_list_str =
        job_list1; end
    if ((var["wudang_jobbusy"] or 0) > 69 and string.find(var["job_list"], "wd")) or ((var["xueshan_jobbusy"] or 0) > 59 and string.find(var["job_list"], "xs")) then job_list_str =
        job_list2_str; end

    -- 从最终确定的任务列表中选择下一个任务
    local nextjob = get_next_job_from_list(job_list_str, lastjob)
    var["job"] = nextjob

    -- 根据Quest进度再次调整任务
    var["job"], var["job_list"] = get_quest_adjusted_job(var["job"], lastjob, var["job_list"])
    if (var["do_log"] or 0) > 0 then log_message("JOB:" .. lastjob .. "->" .. var["job"], C.Y, "<lua>") end

    if var["drop_duzhen"] ~= nil then
        g(58, function()
            var["drop_duzhen"] = nil; exec("put duzhen in zhan;drop duzhen;se;e;changejob")
        end); return
    end

    -- 设置最终状态并调用`prepare_job`开始准备流程
    set_best_dazuo(); set_lua_flags(); check_weapon(); check_cloth()
    if (var["exp"] or 0) > 2900 then prepare_job() end
end)

-- ===================================================================
-- 5. 保留的原始代码 (及小幅修改)
-- 本模块包含一些从旧代码中保留下来且仍在使用的功能函数。
-- ===================================================================

---
-- @function check_weapon
-- @brief 检查和整理玩家背包中的武器。使用carryqty函数和数据驱动的方式简化逻辑。
--        - 清理多余的、不合规的武器（如多于1个的暗器，非5个的法轮等）。
--        - 根据配置（`var["myweapon"]`等）设置状态变量，如 `item["myweapon"]`, `var["buymyweapon"]`。
function check_weapon()
    -- 清理固定规则的物品
    local cleanup_rules = {
        { item_id = "ansha bishou", max_count = 1 },
        { item_id = "falun",        exact_count = 5 },
        { item_id = "fire",         max_count = 2 },
        { item_id = "cu shengzi",   max_count = 1 }
    }

    for _, rule in ipairs(cleanup_rules) do
        local count = carryqty(rule.item_id)
        if rule.exact_count and count ~= rule.exact_count then
            send("drop " .. rule.item_id)
            item[rule.item_id] = 0
        elseif rule.max_count and count > rule.max_count then
            send("drop " .. rule.item_id .. " " .. (count - rule.max_count))
            item[rule.item_id] = rule.max_count
        end
    end

    -- 清理单个物品
    local single_items = { "tie ling", "xiuhua zhen", "shoes", "cao xie" }
    for _, item_id in ipairs(single_items) do
        if carryqty(item_id) > 0 then send("drop " .. item_id) end
    end

    -- 开启盒子
    if carryqty("apbox") > 0 then send("open apbox") end
    if carryqty("dpbox") > 0 then send("open dpbox") end

    -- 检查主武器
    item["myweapon"] = 0
    local w = var["myweapon"] or ""

    -- 特殊武器数量规则（整合到数据表中）
    local special_weapon_rules = {
        ["gun"] = { name = "齐眉棍", min_count = 1 },
        ["bi shou"] = { name = "百辟匕首", min_count = 1 },
        ["jiulong ling"] = { name = "jiulong ling", exact_count = 2 },
        ["coin"] = { name = "coin", min_count = 201 },
        ["mu dao"] = { name = "mu dao", min_count = 10 },
        ["zhen"] = { name = "zhen", min_count = 81 },
        ["falun"] = { name = "falun", exact_count = 5 },
        ["sangmen ding"] = { name = "sangmen ding", min_count = 101 },
        ["jinshe zhui"] = { name = "jinshe zhui", min_count = 6 },
    }

    local rule = special_weapon_rules[w]
    if rule then
        local item_count = carryqty(rule.name)
        local is_valid = false

        if rule.exact_count then
            is_valid = (item_count == rule.exact_count)
            if not is_valid and item_count > rule.exact_count then
                send("drop " .. rule.name .. " " .. (item_count - rule.exact_count))
            end
        elseif rule.min_count then
            is_valid = (item_count >= rule.min_count)
        end

        if is_valid then
            item[w] = item_count
            item["myweapon"] = 1
        end
    elseif carryqty(w) > 0 then
        item["myweapon"] = 1
    end

    -- 检查砍路武器
    item["weapon"] = 0
    var["weapon"] = get_fullname(var["weapon"] or "mu jian")
    if carryqty(var["weapon"]) > 0 then item["weapon"] = 1 end

    -- 检查SMY武器
    item["smy_weapon"] = 0
    local smyweapon = var["smy_weapon"] or "none"
    if smyweapon == "" then smyweapon = "none" end
    if carryqty(smyweapon) > 0 then item["smy_weapon"] = 1 end

    -- 设置购买标志
    var["buymyweapon"] = (rex.match(var["myweapon"], BUYABLE_WEAPON_PATTERN) ~= nil) and 1 or 0
    var["buysmyweapon"] = (rex.match(smyweapon, BUYABLE_WEAPON_PATTERN) ~= nil) and 1 or 0

    local other_weapon = var["other_weapon"] or "none"
    if other_weapon == "" then other_weapon = "none" end
    if rex.match(other_weapon, BUYABLE_WEAPON_PATTERN) == nil then
        var["buyotherweapon"] = 0
    else
        var["buyotherweapon"] = (carryqty(var["other_weapon"]) > 0) and 0 or 1
    end

    -- 清理多余的标准武器
    for k, v in pairs(item) do
        if string.find(STANDARD_WEAPON_LIST_STRING, "|" .. k .. "|") and v > 1 then
            send("drop " .. k .. " 2")
        end
    end
end

---
-- @function check_cloth
-- @brief 检查玩家是否穿着了合法的衣服，如果没有，则设置购买标志。
function check_cloth()
    local has_valid_cloth, current_cloth_id = check_valid_cloth()
    if has_valid_cloth then
        var["current_cloth_id"] = current_cloth_id
    else
        if (var["need_cloth"] or 1) == 1 then var["buycloth"] = 1 end
    end
end

---
-- @trigger repair_weapon_s
-- @brief 当武器断裂时触发，记录日志并发出警告。
add_trigger("repair_weapon_s", "^[ > ]*你手中的(.*)使用过久，终于断为两截！", function(params)
    log_to_txt("lostweapon", var.id)
    log_message("你的武器【" .. params[1] .. "】已断裂！！！！！！！", C.R, "<解密>")
end)

---
-- @function set_best_dazuo
-- @brief 计算并设置最佳的打坐(dazuo)和吐纳(tuna)数值。
--        这些值用于在需要恢复内力和精力时，一次性恢复尽可能多的量，以提高效率。
function set_best_dazuo()
    if not var["dazuo"] then
        if not var["skills_jifalevel"] then
            var["skills_jifalevel"] = { ["force"] = 0 }; log_message("skills_jifalevel 不存在", C.R, "<警告>")
        end
        local force = (var["skills_jifalevel"] and var["skills_jifalevel"]["force"]) or 0
        local neili_gain = 1 + math.floor(force / 15) * (1 + math.floor(force / 60))
        local age = var["age"] or 20; if age < 20 then neili_gain = neili_gain + math.floor(neili_gain * (20 - age) / 10) end
        neili_gain = math.floor(neili_gain * 1.5) * math.ceil((var["maxqi"] or 1000) / 5 / neili_gain); var["dazuo"] =
        neili_gain; log_message("dazuo：" .. var["dazuo"], C.g, "<lua>")
    end
    if not var["tuna"] then
        local force = (var["skills_jifalevel"] and var["skills_jifalevel"]["force"]) or 0
        local jingli_gain = math.floor(1 + force / 10 * (1 + force / 100))
        local age = var["age"] or 20; if age < 20 then jingli_gain = jingli_gain +
            math.floor(jingli_gain * (20 - age) / 10) end
        jingli_gain = math.floor(jingli_gain * 1.5) * math.ceil((var["maxjing"] or 1000) / 5 / jingli_gain); var["tuna"] =
        jingli_gain; log_message("tuna：" .. var["tuna"], C.g, "<lua>")
    end
end

---
-- @alias check_heal
-- @brief 一个封装别名，用于在执行某个动作之前检查角色的受伤状态。
--        如果受伤不重，则继续执行原动作；如果受伤较重，则先去疗伤。
add_alias("check_heal", function(params)
    local check = params[-1]; var["check_heal"] = check; add_alias("after_check_heal", function(params) exec(check) end)
    add_trigger("check_heal", "^[ > ]*你把\\s+\"action\"\\s+设定为\\s+\"检查受伤", function(params)
        del_timer("input"); if (var["hurtqi"] or 100) > 94 then
            del_trigger("check_heal"); if var["check_heal"] == "do_job_cisha" then
                set_dazuo("cisha"); exec("yun jing;yun jingli;yun qi;go_dazuo");
            else exec("after_check_heal"); end
        else
            exec("do_healqi @check_heal");
        end
    end)
    send("time -s;hp " .. var["char_id"]); exes("alias action 检查受伤...", 2)
end)

---
-- @alias check_poison
-- @brief 一个封装别名，用于在执行某个动作之前检查角色的中毒状态。
--        如果未中毒，则继续；如果中毒，则尝试解毒。
add_alias("check_poison", function(params)
    local check = params[-1]; var["check_poison"] = check; add_alias("after_cond", function(params) exec(check) end)
    add_trigger("check_poison", "^[ > ]*你把\\s+\"action\"\\s+设定为\\s+\"检查中毒", function(params)
        unset_timer("check"); del_timer("input"); local cond = var["cond"] or 0
        if cond == 0 then
            del_trigger("check_poison"); check_busy(function() exec("after_cond") end)
        elseif cond == 2 then
            if (var["poison_super_star"] or 0) == 1 then
                del_trigger("check_poison"); check_busy(function() check_place(function()
                        exec("yun liaodu " .. var["char_id"] .. ";yun qudu " .. var["char_id"]); check_busy(function()
                            exec("after_cond") end)
                    end) end)
            else
                if (item["田七鲨胆散"] or 0) > 0 then
                    del_trigger("check_poison"); check_busy(function()
                        exec("eat tianqi"); check_busy(function() exec("after_cond") end)
                    end)
                else exec("liaodu"); end
            end
        else
            exec("check_cond @check_poison");
        end
    end)
    send("time -s;cond"); exes("alias action 检查中毒...", 2)
end)

---
-- @alias liaodu
-- @brief 当没有解毒药时，启动疗毒的流程。
add_alias("liaodu", function(params)
    add_trigger("check_poison", "^[ > ]*你把\\s+\"action\"\\s+设定为\\s+\"开始疗毒", function(params)
        del_timer("input")
        if (var["cond"] or 0) > 0 then
            if (var["hurtjing"] or 100) < 70 then
                check_busy2(function()
                    liaodu_heal(); exec("set heal jing;yun jing;yun qi;yun qudu " ..
                    var["char_id"] .. ";yun liaodu " .. var["char_id"] .. ";yun heal")
                end)
            elseif (var["hurtqi"] or 100) < 70 then
                check_busy2(function()
                    liaodu_heal(); exec("unset heal;yun jing;yun qi;yun qudu " ..
                    var["char_id"] .. ";yun liaodu " .. var["char_id"] .. ";yun heal")
                end)
            elseif var["hurtqi"] == 100 and var["hurtjing"] == 100 then
                wait(10, function()
                    send("cond;hp"); exes("alias action 开始疗毒...", 2)
                end)
            else
                check_busy2(function()
                    liaodu_heal(); exes(
                    "set heal jing;yun jing;yun qi;yun qudu " ..
                    var["char_id"] .. ";yun liaodu " .. var["char_id"] .. ";yun heal", 2)
                end)
            end
        else
            exec("changejob");
        end
    end)
    check_place(function()
        send("cond;hp"); exes("alias action 开始疗毒...", 2)
    end)
end)

---
-- @function liaodu_heal
-- @brief 为`liaodu`别名设置一系列的触发器，用于管理疗伤和疗毒过程中的各种情况。
function liaodu_heal()
    add_alias("after_heal", function(params) exes("cond;hp;alias action 开始疗毒...", 2) end)
    add_alias("yun_heal", function(params) exes("yun heal", 2) end)
    add_trigger("heal_1", "^[ > ]*(?:你并没有受伤！|没受伤疗什么伤？)",
        function(params)
            del_timer("input"); var["no_need_heal_qi"] = 1; close_heal(); exec("after_heal")
        end)
    add_trigger("heal_2", "^[ > ]*(?:你呼出一口气站了起来，可惜伤势还没有完全恢复。|你的真气不够。)",
        function(params)
            del_timer("timer"); set_dazuo("health"); wait(3, function()
                send("yun qi"); exec("go_dazuo")
            end)
        end)
    add_trigger("heal_3",
        "^[ > ]*你(?:连催四道冷泉内劲|运起寒冰真气，开始缓缓运气疗伤。|收蹑心神，屏息静气，缓缓开始运功疗伤。|双手合什，盘膝而坐|神情肃然，双目虚闭|全身放松，半蹲在地上|盘膝坐下，依照经中所示|盘膝坐下，蓦然想起|盘膝坐下，开始运功疗伤。|盘膝而坐，双手十指张开|凝神静气，内息随悠扬箫声)",
        function(params)
            del_timer("input"); var["idle"] = 0; del_timer("timer"); del_timer("wait"); del_timer("idle")
        end)
    add_trigger("heal_4", "^[ > ]*你还没有选择你要使用的内功。",
        function(params)
            del_timer("input"); check_busy2(function()
                send("jifa all;jifa force " .. var["jifa force"]); exec("yun_heal")
            end)
        end)
    add_trigger("heal_5", "^[ > ]*你已经受伤过重，经受不起真气震荡！", function(params)
        del_timer("input")
        if carryqty("dahuan dan") > 0 and ((var["usedahuandan"] or 0) == 1 or (var["usedahuandan"] or 0) == 3) then
            send("fu dan"); close_heal(); exec("after_heal")
        elseif carryqty("chantui yao") > 0 then
            set_dazuo("heal"); check_place(function()
                send("fu chantui yao;yun jing;yun qi;hp;i"); exec("yun_heal")
            end)
        elseif carryqty("gold") < 4 then
            wait(3, function() exec("do_qu 5 gold") end)
        else
            wait(3, function() exec("do_buy chantui yao") end)
        end
    end)
    add_trigger("heal_6", "^[ > ]*(\\S+)并没有受伤！",
        function(params) if string.find(params[1], var["char_name"]) then
                del_timer("input"); close_heal(); exec("after_heal")
            end end)
    add_trigger("heal_7",
        "^[ > ]*(?:你|良久，你|九阳神功的威力，这时方才显现出来，在你|过了良久，琴音顿止，你|就一眨眼功夫，你)(?:双眼缓缓睁开|运功完毕，站起身来，看上去气色饱满，精神抖擞。|长吸一口气，精神抖擞的站了起来。|体内又运走数转|脸上流光浮现|脸色渐渐变得舒缓|将草木精华与内息|缓缓站起，只觉全身说不出的舒服畅快|感觉通过自己的内息运行|“哇！”的大叫一声)",
        function(params)
            var["idle"] = 0; close_heal(); exec("after_heal")
        end)
end

---
-- @alias check_cond
-- @brief 处理轻微中毒状态的别名。它会不断地尝试恢复，直到中毒状态解除。
add_alias("check_cond", function(params)
    local check_cond = params[-1]
    local after_actions = { ["changejob"] = "changejob", ["do_prepare_sx2"] = "do_prepare_sx2",
        ["check_heal do_job_cisha"] = "check_heal do_job_cisha", ["check_heal do_job_smy"] = "check_heal do_job_smy",
        ["check_heal do_job_swxy"] = "check_heal do_job_swxy", ["check_heal do_job_huoshao"] =
    "check_heal do_job_huoshao", ["job_fail"] = "job_fail", ["job_win"] = "job_win", ["do_job_sx"] = "do_job_sx" }
    if after_actions[check_cond] then add_alias("after_cond", function() exec(after_actions[check_cond]) end) end
    set_dazuo("none")
    add_trigger("check_cond", "^[ > ]*你把\\s+\"action\"\\s+设定为\\s+\"中毒恢复中...", function(params)
        del_timer("input"); if var["cond"] == 0 then
            del_timer("timer"); del_trigger("check_cond"); send("halt;unset heal"); check_busy(function() exec(
                "after_cond") end)
        elseif var["cond"] == 2 then
            if (var["poison_super_star"] or 0) == 1 then
                del_trigger("check_poison"); check_busy(function() check_place(function()
                        exec("yun liaodu " .. var["char_id"] .. ";yun qudu " .. var["char_id"]); check_busy(function()
                            exec("after_cond") end)
                    end) end)
            elseif (item["田七鲨胆散"] or 0) > 0 then
                check_busy(function()
                    exec("eat tianqi;i"); check_busy(function() exec("after_cond") end)
                end)
            else
                set_dazuo("none"); unset_timer("timer"); unset_timer("wait"); check_busy(function() exec(
                    "do_quit poison") end)
            end
        end
    end)
    local function perform_recovery()
        send("halt;unwield " .. var["myweapon"] .. ";unwield " .. var["weapon"] .. ";unwield " .. var["smy_weapon"])
        if math.random(2) == 2 then send("set heal jing") else send("unset heal") end
        send("yun liaojing;yun qudu;yun liaodu;yun qi;yun jing;perform liao;yun liao;yun juxue;yun heal")
        send("hp " .. var["char_id"] .. ";cond;dazuo " .. (var["dazuo"] or 1000) .. ";alias action 中毒恢复中...")
    end
    var["cond"] = 0; perform_recovery(); set_timer("timer", 5, function()
        var["cond"] = 0; perform_recovery()
    end)
end)

---
-- @function set_lua_flags
-- @brief 设置一系列用于寻路的布尔标志 (`lua_flags`)。
--        这些标志根据角色的门派、性别、技能、游戏时间等状态来设定，
--        用于告知寻路系统哪些地图区域是可用的或禁用的。
function set_lua_flags()
    lua_flags = standard_flags
    if not var["skills_level"] then var["skills_level"] = {} end; local party = var["party"] or ""
    local qimen_level = tonumber(var["skills_level"]["qimen-bagua"] or "0")
    if string.find(party, "桃花岛") then
        if qimen_level > 130 then
            lua_flags["taohuadao"] = 0; lua_flags["party_thd"] = 0; lua_flags["max_thd"] = 0
        else
            lua_flags["taohuadao"] = 1; lua_flags["party_thd"] = 1; lua_flags["max_thd"] = 1
        end
    else
        lua_flags["taohuadao"] = 1; lua_flags["party_thd"] = 1; lua_flags["max_thd"] = 1
        if qimen_level > 150 then lua_flags["taohuadao"] = 0 end
    end
    if string.find(party, "天龙寺") and string.find(var["master"] or "", "一灯大师") then
        lua_flags["master_yideng"] = 0; lua_flags["mix_yideng"] = 0;
    end
    if string.find(party, "明教") and string.find(var["master"] or "", "张无忌") then lua_flags["hudiegu"] = 0; end
    if string.find(party, "古墓派") and (string.find(var["master"] or "", "杨过") or string.find(var["master"] or "", "小龙女")) then
        lua_flags["party_yangguo"] = 0; if var["mapjueqinggu"] == nil then add_map("增加古墓绝情谷底"); end
    end
    if string.find(party, "武当派") and string.find(var["master"] or "", "张三丰") then
        lua_flags["master_notzhangsanfeng"] = 0; lua_flags["master_zhangsanfeng"] = 1;
    else
        lua_flags["master_notzhangsanfeng"] = 1; lua_flags["master_zhangsanfeng"] = 0;
    end
    if string.find(party, "昆仑派") then lua_flags["party_kunlun"] = 0; end
    if string.find(party, "神龙") then lua_flags["shenlongdao"] = 0; end
    if string.find(party, "姑苏慕容") then lua_flags["murong"] = 0; end
    if not string.find(party, "神龙") then lua_flags["shenlongdao"] = 0; end
    local sex = var["sex"] or "无性"; lua_flags["mix_thd_male"], lua_flags["mix_thd_female"] = 1, 1; lua_flags["mix_yideng_male"], lua_flags["mix_yideng_female"] =
    1, 1;
    if string.find(sex, "男性") then
        lua_flags["mix_thd_female"] = 0; lua_flags["mix_yideng_female"] = 0;
    end
    if string.find(sex, "女性") then
        lua_flags["mix_thd_male"] = 0; lua_flags["mix_yideng_male"] = 0;
    end
    local time_num = 8; if type(var["time_number"]) == "number" then time_num = var["time_number"] elseif type(var["time_number"]) == "string" and tonumber(var["time_number"]) then time_num =
        tonumber(var["time_number"]) end
    lua_flags["night"] = (time_num < 5) and 1 or 0; lua_flags["shop"] = (time_num > 1 and time_num < 7) and 1 or 0
    if var["must_be_night"] then
        lua_flags["night"] = 1; lua_flags["shop"] = 1;
    end
    lua_flags["xiaofu"] = ((var["mapxiaofu"] or 0) == 1) and 0 or 1; lua_flags["wudujiao"] = 1
    if ((var["mapwudujiao"] or 0) == 1 and (var["close_wudujiao"] or 1) ~= 1) or string.find(var["jifa_force"] or "", "jiuyang%-shengong") then
        lua_flags["wudujiao"] = 0; if string.find(var["jifa_force"] or "", "jiuyang%-shengong") then var["mapwudujiao"] = 1; end
    end
    lua_flags["yideng"] = ((var["exp"] or 0) > 800000 and (var["close_yideng"] or 1) ~= 1) and 0 or 1
    lua_flags["wudanghoushan"] = ((var["exp"] or 0) > 800000 and (var["close_wudang"] or 1) ~= 1 and ((var["skills_level"] and var["skills_level"]["hand"]) or 0) > 100) and
    0 or 1
    lua_flags["male"], lua_flags["female"] = 0, 0
    if string.find(sex, "男性") then lua_flags["male"] = 1; elseif string.find(sex, "女性") then lua_flags["female"] = 1; else
        lua_flags["male"] = 1; lua_flags["female"] = 1;
    end
end

---
-- @function get_time
-- @brief 获取当前系统时间的简单封装。
-- @return (number) 当前的Unix时间戳。
function get_time()
    return os.time()
end

Print("--- 加载模块: 任务准备 ---")
