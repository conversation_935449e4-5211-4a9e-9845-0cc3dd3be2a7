-- fight.lua - 战斗系统模块
-- 负责处理游戏中的战斗事件和状态，包括敌方特殊技能应对、角色状态监控、自动吃药、武器管理、战斗胜负判断和任务特定逻辑。
-- 模块通过大量触发器和辅助函数实现了战斗流程的自动化和智能化。
-- 作者: Cline
-- 修订日期: 2025-07-03

-- fight 模块变量定义
-- var["fight_win"]=1 战斗胜利结束标志
-- var["fight_pause"]=1 战斗暂停标志
-- var["fight_quit"]=1 战斗逃跑标志
-- var["killer_skill"] 敌方杀手的技能
-- var["killer_party"] 敌方杀手的门派

-- 1) 打不过要逃跑：处理特定高威胁技能，决定是否逃跑
-- fight_1 触发器: 七伤拳应对
-- 当敌人使用七伤拳时触发。如果不是smy或swxy任务，则执行job_escape；否则根据smy_escape/swxy_escape决定是否逃跑，或硬抗。
add_trigger("fight_1","^\\s*\\S+突然间大喝一声，将所积蓄的十余掌的力道归并成为一掌迅急拍向你。",function(params)
--七伤拳qishang
	if var["flag_job"]~="smy" or var["flag_job"]~="swxy" then
		exec("job_escape")
	else  --smy战斗中另行处理
		local smy_escape=var["smy_escape"] or 1
		local swxy_escape=var["swxy_escape"] or 1
		if smy_escape==1 or swxy_escape==1 then
			exec("job_escape")
		else
			--如果不想跑强力党就硬挺吧！
			echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."遇到七伤拳七伤，但强力党继续战斗！")	
		end
	end
	
end)
-- fight_2 触发器: 太极拳雪崩应对
-- 当敌人使用太极拳雪崩时触发。逻辑同fight_1。
add_trigger("fight_2","^\\s*\\S+掌势蕴含太极之力，连绵不断，如同雪崩般袭向你！",function(params)
--雪崩xuebeng
	if var["flag_job"]~="smy" or var["flag_job"]~="swxy" then
		exec("job_escape")
	else  --smy战斗中另行处理
		local smy_escape=var["smy_escape"] or 1
		local swxy_escape=var["swxy_escape"] or 1
		if smy_escape==1 or swxy_escape==1 then
			exec("job_escape")
		else
			--如果不想跑强力党就硬挺吧！
			echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."遇到太极拳雪崩，但强力党继续战斗！")	
		end
	end
end)
-- fight_3 触发器: 烈焰焚身应对
-- 当敌人使用烈焰焚身时触发。逻辑同fight_1。
add_trigger("fight_3","^\\s*\\S*发出两道烈焰瞬间将你裹在火中。",function(params)
--fen
	if var["flag_job"]~="smy" or var["flag_job"]~="swxy" then
		exec("job_escape")
	else  --smy战斗中另行处理
		local smy_escape=var["smy_escape"] or 1
		local swxy_escape=var["swxy_escape"] or 1
		if smy_escape==1 or swxy_escape==1 then
			exec("job_escape")
		else
			--如果不想跑强力党就硬挺吧！
			echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."遇到烈焰焚身，但强力党继续战斗！")	
		end
	end
end)
-- fight_4 触发器: 精神冲击应对
-- 当玩家被吓坏时触发。逻辑同fight_1。
add_trigger("fight_4","^\\s*你被.*吓坏了。",function(params)
--??
	if var["flag_job"]~="smy" or var["flag_job"]~="swxy" then
		exec("job_escape")
	else  --smy战斗中另行处理
		local smy_escape=var["smy_escape"] or 1
		local swxy_escape=var["swxy_escape"] or 1
		if smy_escape==1 or swxy_escape==1 then
			exec("job_escape")
		else
			--如果不想跑强力党就硬挺吧！
			echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."遇到精神冲击被吓坏了，但强力党继续战斗！")	
		end
	end
end)
-- fight_5 触发器: 烈焰冲击全身应对
-- 当玩家全身着火时触发。逻辑同fight_1。
add_trigger("fight_5","^\\s*你身上衣衫毛发尽皆着火，皮肤头发被烧得吱吱做响，滚倒在地，不住翻滚号哭！",function(params)
--fen
	if var["flag_job"]~="smy" or var["flag_job"]~="swxy" then
		exec("job_escape")
	else  --smy战斗中另行处理
		local smy_escape=var["smy_escape"] or 1
		local swxy_escape=var["swxy_escape"] or 1
		if smy_escape==1 or swxy_escape==1 then
			exec("job_escape")
		else
			--如果不想跑强力党就硬挺吧！
			echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."遇到烈焰冲击全身，但强力党继续战斗！")	
		end
	end
end)
-- fight_6 触发器: 太极拳借力打力应对
-- 当敌人使用太极拳借力打力时触发。逻辑同fight_1。
add_trigger("fight_6","^\\s*你眼看不对，急中生智奋力一挣！",function(params)
--太极拳
	if var["flag_job"]~="smy" or var["flag_job"]~="swxy" then
		exec("job_escape")
	else  --smy战斗中另行处理
		local smy_escape=var["smy_escape"] or 1
		local swxy_escape=var["swxy_escape"] or 1
		if smy_escape==1 or swxy_escape==1 then
			exec("job_escape")
		else
			--如果不想跑强力党就硬挺吧！
			echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."遇到太极拳借力打力，但强力党继续战斗！")	
		end
	end
end)
-- fight_7 触发器: 独孤九剑总决应对 (克制无还手之力)
-- 当敌人使用独孤九剑总决且玩家被克制时触发。逻辑同fight_1。
add_trigger("fight_7","^\\s*你只觉得\\S+每一招都将你克制的毫无还手之余！",function(params)
--独孤九剑zongjue
	if var["flag_job"]~="smy" or var["flag_job"]~="swxy" then
		exec("job_escape")
	else  --smy战斗中另行处理
		local smy_escape=var["smy_escape"] or 1
		local swxy_escape=var["swxy_escape"] or 1
		if smy_escape==1 or swxy_escape==1 then
			exec("job_escape")
		else
			--如果不想跑强力党就硬挺吧！
			echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."遇到独孤九剑总决，但强力党继续战斗！")	
		end
	end
end)
-- fight_8 触发器: 独孤九剑总决应对 (武功无法发挥)
-- 当敌人使用独孤九剑总决且玩家武功无法发挥时触发。逻辑同fight_1。
add_trigger("fight_8","^\\s*你只觉得处处受制，武功中厉害之处完全无法发挥出来！",function(params)
--独孤九剑zongjue
	if var["flag_job"]~="smy" or var["flag_job"]~="swxy" then
		exec("job_escape")
	else  --smy战斗中另行处理
		local smy_escape=var["smy_escape"] or 1
		local swxy_escape=var["swxy_escape"] or 1
		if smy_escape==1 or swxy_escape==1 then
			exec("job_escape")
		else
			--如果不想跑强力党就硬挺吧！
			echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."遇到独孤九剑被克制，但强力党继续战斗！")	
		end
	end
end)

-- 2) 战斗中吃药
-- fight_9 触发器: 服用大还丹
-- 当玩家服用大还丹并看到相应提示时触发。更新大还丹数量，并根据当前任务（smy/swxy）执行后续逻辑。
add_trigger("fight_9","^\\s*你吃下一颗大还丹，觉得丹田处有暖流涌上，顿时伤势痊愈气血充盈。",function(params)
	send("i")
	item=item or {}
	local dahuandan=item["dahuan dan"] or 0
--	dahuandan=dahuandan-1
	item["dahuan dan"]=dahuandan
	if dahuandan<1 then
		item["dahuan dan"]=0
	end
	if var["flag_job"]=="smy" then
		--颂摩崖上吃了大还丹，说明要接着战斗那么继续调用look wushi 1,look wushi 2
		echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."颂摩崖吃了大还丹了，继续战斗！")
		send("kill xixia wushi 1")
		send("kill xixia wushi 2")

		if var["wushi1_faint"]=="武士1无威胁" then --转目标武士2
			var["fight"]=1
			var["pfm_id"]=var["wushi2_id"]
			local sp_pfm_exist=false
			for i=1,10 do  --10 个技能和对应的Pfm，应该够用了吧？
				if var["smy_sp_skill"..i]==nil then --如果未设置特殊pfm，那么强制设置为空字符串 防止比较过程中出错
					var["smy_sp_skill"..i]=""
				end
				if var["smy_sp_skill"..i] == var["wushi2_skill"] then
					--echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."【战颂摩崖】：发现特殊技能列表【"..var["smy_sp_skill"..i].."】与【"..var["wushi1_skill"].."】匹配！")					
					sp_pfm_exist=true  											--特殊技能有匹配，那么不能用默认值
					send("alias pfm "..expand(var["smy_sp_pfm"..i]))  		--特殊技能对应特殊pfm如果有匹配的话，那么pfm设置为对应的特殊pfm
					send("alias pfm_backup "..expand(var["smy_sp_pfm"..i]))
				end
			end
			if not sp_pfm_exist then  --没有匹配的特殊Pfm，那么设置为默认的var["smy_pfm"]
				send("alias pfm "..expand(var["smy_pfm"]))
				send("alias pfm_backup "..expand(var["smy_pfm"]))
			end
			send("set wimpy 100")
			send("set wimpycmd hp "..var["char_id"].."\\pfm")
			send("pfm")
		end
			------------------------------------
		if var["wushi2_faint"]=="武士2无威胁" then --转目标武士1
			var["fight"]=1
			var["pfm_id"]=var["wushi1_id"]
			local sp_pfm_exist=false
			for i=1,10 do  --10 个技能和对应的Pfm，应该够用了吧？
				if var["smy_sp_skill"..i]==nil then --如果未设置特殊pfm，那么强制设置为空字符串 防止比较过程中出错
					var["smy_sp_skill"..i]=""
				end
				if var["smy_sp_skill"..i] == var["wushi1_skill"] then
					--echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."【战颂摩崖】：发现特殊技能列表【"..var["smy_sp_skill"..i].."】与【"..var["wushi1_skill"].."】匹配！")					
					sp_pfm_exist=true  											--特殊技能有匹配，那么不能用默认值
					send("alias pfm "..expand(var["smy_sp_pfm"..i]))  		--特殊技能对应特殊pfm如果有匹配的话，那么pfm设置为对应的特殊pfm
					send("alias pfm_backup "..expand(var["smy_sp_pfm"..i]))
				end
			end
			if not sp_pfm_exist then  --没有匹配的特殊Pfm，那么设置为默认的var["smy_pfm"]
				send("alias pfm "..expand(var["smy_pfm"]))
				send("alias pfm_backup "..expand(var["smy_pfm"]))
			end
			send("set wimpy 100")
			send("set wimpycmd hp "..var["char_id"].."\\pfm")
			send("pfm")
		end
		if var["wushi1_faint"]~="武士1无威胁" and var["wushi2_faint"]~="武士2无威胁" then
			exec("look xixia wushi 1;look xixia wushi 2")
		end
	elseif var["flag_job"]=="swxy" then
		--颂摩崖上吃了大还丹，说明要接着战斗那么继续调用look wushi 1,look wushi 2
		echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."颂摩崖吃了大还丹了，继续战斗！")
		send("kill menggu wushi 1")
		send("kill menggu wushi 2")

		if var["wushi1_faint"]=="武士1无威胁" then --转目标武士2
				var["fight"]=1
				var["pfm_id"]=var["wushi2_id"]
				local sp_pfm_exist=false
				for i=1,10 do  --10 个技能和对应的Pfm，应该够用了吧？
					if var["swxy_sp_skill"..i]==nil then --如果未设置特殊pfm，那么强制设置为空字符串 防止比较过程中出错
						var["swxy_sp_skill"..i]=""
					end
					if var["swxy_sp_skill"..i] == var["wushi2_skill"] then
						--echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."【战颂摩崖】：发现特殊技能列表【"..var["smy_sp_skill"..i].."】与【"..var["wushi1_skill"].."】匹配！")					
						sp_pfm_exist=true  											--特殊技能有匹配，那么不能用默认值
						send("alias pfm "..expand(var["swxy_sp_pfm"..i]))  		--特殊技能对应特殊pfm如果有匹配的话，那么pfm设置为对应的特殊pfm
						send("alias pfm_backup "..expand(var["swxy_sp_pfm"..i]))
					end
				end
				if not sp_pfm_exist then  --没有匹配的特殊Pfm，那么设置为默认的var["smy_pfm"]
					send("alias pfm "..expand(var["swxy_pfm"]))
					send("alias pfm_backup "..expand(var["swxy_pfm"]))
				end
				send("set wimpy 100")
				send("set wimpycmd hp "..var["char_id"].."\\pfm")
				send("pfm")
		end
			------------------------------------
		if var["wushi2_faint"]=="武士2无威胁" then --转目标武士1
				var["fight"]=1
				var["pfm_id"]=var["wushi1_id"]
				local sp_pfm_exist=false
				for i=1,10 do  --10 个技能和对应的Pfm，应该够用了吧？
					if var["swxy_sp_skill"..i]==nil then --如果未设置特殊pfm，那么强制设置为空字符串 防止比较过程中出错
						var["swxy_sp_skill"..i]=""
					end
					if var["swxy_sp_skill"..i] == var["wushi1_skill"] then
						--echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."【战颂摩崖】：发现特殊技能列表【"..var["smy_sp_skill"..i].."】与【"..var["wushi1_skill"].."】匹配！")					
						sp_pfm_exist=true  											--特殊技能有匹配，那么不能用默认值
						send("alias pfm "..expand(var["swxy_sp_pfm"..i]))  		--特殊技能对应特殊pfm如果有匹配的话，那么pfm设置为对应的特殊pfm
						send("alias pfm_backup "..expand(var["swxy_sp_pfm"..i]))
					end
				end
				if not sp_pfm_exist then  --没有匹配的特殊Pfm，那么设置为默认的var["smy_pfm"]
					send("alias pfm "..expand(var["swxy_pfm"]))
					send("alias pfm_backup "..expand(var["swxy_pfm"]))
				end
				send("set wimpy 100")
				send("set wimpycmd hp "..var["char_id"].."\\pfm")
				send("pfm")
			end
		if var["wushi1_faint"]~="武士1无威胁" and var["wushi2_faint"]~="武士2无威胁" then
			exec("look menggu wushi 1;look menggu wushi 2")
		end
	else
		send("set wimpy 100")
		send("set wimpycmd hp "..var["char_id"].."\\pfm")
	end
end)
-- fight_10 触发器: 服用川贝内息丸
-- 当玩家服用川贝内息丸并看到相应提示时触发。更新川贝内息丸数量，并根据当前任务（smy）执行后续逻辑。
add_trigger("fight_10","^\\s*你服下一颗川贝内息丸，顿时感觉内力充沛。",function(params)
	item=item or {}
	local dahuandan=item["chuanbei wan"] or 0
	dahuandan=dahuandan-1
	item["chuanbei wan"]=dahuandan
	if dahuandan<1 then
		item["chuanbei wan"]=0
	end
	if var["flag_job"]=="smy" then
		--颂摩崖上吃了川贝丸，说明要接着战斗那么继续调用look wushi 1,look wushi 2
		echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."【战颂摩崖】：颂摩崖吃了川贝丸，继续战斗！")
		send("kill xixia wushi 1")
		send("kill xixia wushi 2")

		if var["wushi1_faint"]=="武士1无威胁" then --转目标武士2
				var["fight"]=1
				var["pfm_id"]=var["wushi2_id"]
				local sp_pfm_exist=false
				for i=1,10 do  --10 个技能和对应的Pfm，应该够用了吧？
					if var["smy_sp_skill"..i]==nil then --如果未设置特殊pfm，那么强制设置为空字符串 防止比较过程中出错
						var["smy_sp_skill"..i]=""
					end
					if var["smy_sp_skill"..i] == var["wushi2_skill"] then
						--echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."【战颂摩崖】：发现特殊技能列表【"..var["smy_sp_skill"..i].."】与【"..var["wushi1_skill"].."】匹配！")					
						sp_pfm_exist=true  											--特殊技能有匹配，那么不能用默认值
						send("alias pfm "..expand(var["smy_sp_pfm"..i]))  		--特殊技能对应特殊pfm如果有匹配的话，那么pfm设置为对应的特殊pfm
						send("alias pfm_backup "..expand(var["smy_sp_pfm"..i]))
					end
				end
				if not sp_pfm_exist then  --没有匹配的特殊Pfm，那么设置为默认的var["smy_pfm"]
					send("alias pfm "..expand(var["smy_pfm"]))
					send("alias pfm_backup "..expand(var["smy_pfm"]))
				end
				send("set wimpy 100")
				send("set wimpycmd hp "..var["char_id"].."\\pfm")
				send("pfm")
		end
			------------------------------------
		if var["wushi2_faint"]=="武士2无威胁" then --转目标武士1
				var["fight"]=1
				var["pfm_id"]=var["wushi1_id"]
				local sp_pfm_exist=false
				for i=1,10 do  --10 个技能和对应的Pfm，应该够用了吧？
					if var["smy_sp_skill"..i]==nil then --如果未设置特殊pfm，那么强制设置为空字符串 防止比较过程中出错
						var["smy_sp_skill"..i]=""
					end
					if var["smy_sp_skill"..i] == var["wushi1_skill"] then
						--echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."【战颂摩崖】：发现特殊技能列表【"..var["smy_sp_skill"..i].."】与【"..var["wushi1_skill"].."】匹配！")					
						sp_pfm_exist=true  											--特殊技能有匹配，那么不能用默认值
						send("alias pfm "..expand(var["smy_sp_pfm"..i]))  		--特殊技能对应特殊pfm如果有匹配的话，那么pfm设置为对应的特殊pfm
						send("alias pfm_backup "..expand(var["smy_sp_pfm"..i]))
					end
				end
				if not sp_pfm_exist then  --没有匹配的特殊Pfm，那么设置为默认的var["smy_pfm"]
					send("alias pfm "..expand(var["smy_pfm"]))
					send("alias pfm_backup "..expand(var["smy_pfm"]))
				end
				send("set wimpy 100")
				send("set wimpycmd hp "..var["char_id"].."\\pfm")
				send("pfm")
			end
		if var["wushi1_faint"]~="武士1无威胁" and var["wushi2_faint"]~="武士2无威胁" then
			exec("look xixia wushi 1;look xixia wushi 2")
		end
	end
end)

-- 3) 武器被夺
-- fight_11 触发器: 武器被夺走 (跃起抢夺)
-- 当武器被敌人跃起抢走时触发。如果不是smy/swxy任务，则尝试重新装备武器；否则根据smy_escape/swxy_escape决定是否逃跑，或继续战斗。
add_trigger("fight_11","^\\s*.*提气纵身，跃起丈余，将半空中的.*抢在手中。",function(params)
--掉地上了？？
	
	if var["flag_job"]~="smy" or var["flag_job"]~="swxy" then
		--exec("job_escape")
		send("set wimpycmd halt\\get "..var["myweapon"].."\\unwield "..var["weapon"].."\\unwield "..var["smy_weapon"].."\\wield "..var["myweapon"])
	else  --smy战斗中另行处理
		local smy_escape=var["smy_escape"] or 1
		local swxy_escape=var["swxy_escape"] or 1
		if smy_escape==1 or swxy_escape==1 then
			exec("job_escape")
		else
			--如果不想跑强力党就捡起武器，继续战斗吧！
			--send("set wimpycmd get "..var["myweapon"].."\\unwield "..var["weapon"].."\\wield "..var["myweapon"])
			echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."武器掉落，但强力党捡起武器继续战斗！请自己在smy_pfm中增加get weapon;wield weapn功能。")	
		end
	end
end)
-- fight_12 触发器: 武器被夺走 (顺势夺过)
-- 当武器被敌人顺势夺走时触发。逻辑同fight_11。
add_trigger("fight_12","^\\s*.*顺势夺过你的",function(params)
--	武器没夺走掉地上么？
	--send("set wimpycmd halt\\get "..var["myweapon"].."\\unwield "..var["weapon"].."\\wield "..var["myweapon"])
	if var["flag_job"]~="smy" or var["flag_job"]~="swxy" then
		--exec("job_escape")
		send("set wimpycmd halt\\get "..var["myweapon"].."\\unwield "..var["weapon"].."\\unwield "..var["smy_weapon"].."\\wield "..var["myweapon"])
	else  --smy战斗中另行处理
		local smy_escape=var["smy_escape"] or 1
		local swxy_escape=var["swxy_escape"] or 1
		if smy_escape==1 or swxy_escape==1 then
			exec("job_escape")
		else
			--如果不想跑强力党就捡起武器，继续战斗吧！
			--send("set wimpycmd get "..var["myweapon"].."\\unwield "..var["weapon"].."\\wield "..var["myweapon"])
			echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."武器掉落，但强力党捡起武器继续战斗！请自己在smy_pfm中增加get weapon;wield weapn功能。")	
		end
	end
end)
-- fight_13 触发器: 武器彻底没了 (多种情况)
-- 当出现“身上没有这样东西”、“无法使用五轮大转”、“铜钱用完了”等提示时触发，表示武器彻底丢失。
-- 如果不是smy/swxy任务，根据noweaponquit决定是否逃跑或切换备用pfm；否则根据smy_escape/swxy_escape决定是否逃跑，或使用备用武器。
add_trigger("fight_13","^\\s*你\\(?:身上没有这样东西\\|现在无法使用「五轮大转」\\|掏了掏背囊，发现里面\\|的铜钱用完了！\\)",function(params)
	
	if var["flag_job"]~="smy" or var["flag_job"]~="swxy" then
		var["noweaponquit"]=var["noweaponquit"] or 1
		if type(var["noweaponquit"])~="number" or var["noweaponquit"]~=1 then --不quit
			send("alias pfm_backup "..expand(var["pfm6"]))
			send("set wimpy 100")
			send("set wimpycmd hp "..var["char_id"].."\\pfm_backup")
		else --quit
			exec("job_escape")
		end
	else  --smy战斗中另行处理
		local smy_escape=var["smy_escape"] or 1
		local swxy_escape=var["swxy_escape"] or 1
		if smy_escape==1 or swxy_escape==1 then
			exec("job_escape")
		else
			--如果不想跑强力党就捡起武器，继续战斗吧！
			send("set wimpycmd get "..var["smy_weapon"].."\\unwield "..var["weapon"].."\\unwield "..var["myweapon"].."\\wield "..var["smy_weapon"])
			echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."武器彻底没了，但强力党使用备用武器继续战斗！")
			if var["smy_pfm_bk"] and var["smy_pfm_bk"]~="" then --使用自己自带的备份武器，比如商店货
				send("alias smy_pfm_bk "..expand(var["smy_pfm_bk"]))
				send("set wimpy 100")
				send("set wimpycmd hp "..var["char_id"].."\\smy_pfm_bk")
			else --如果没有设置备用武器pfm，那么还是逃跑吧。
				echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."武器彻底没了，而且没有设置颂摩崖备用武器pfm而逃跑。")
				echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."请设置颂摩崖备用武器pfm，配置文件中增加smy_pfm_bk变量！pfm中自己写上武器的wield，pfm对象建议直接xixia wushi 1,xixia wushi 2。")
				exec("job_escape")
			end
		end
	end
end)


-- 4) 武器掉了
-- fight_14 触发器: 武器掉落 (多种原因)
-- 当武器因各种原因脱手、掉落时触发。如果不是smy/swxy任务，则尝试重新装备武器；否则根据smy_escape/swxy_escape决定是否逃跑，或继续战斗。
add_trigger("fight_14","^\\s*.*你.*\\(?:一番疾攻。不由的闪身急躲,只能任\\|再也拿捏不住，脱手而出\\|不由自主地把手\\|觉得压力骤增，手腕一麻，手中\\|情急之下只好放弃了手中的兵刃\\|一个把握不住，手中兵器被挑飞了出去\\|再也把持不住，当啷一声掉落在了地上。\\)",function(params)
	--send("set wimpycmd halt\\get "..var["myweapon"].."\\unwield "..var["weapon"].."\\unwield "..var["smy_weapon"].."\\wield "..var["myweapon"])
	if var["flag_job"]~="smy" or var["flag_job"]~="swxy" then
		--exec("job_escape")
		send("set wimpycmd halt\\get "..var["myweapon"].."\\unwield "..var["weapon"].."\\unwield "..var["smy_weapon"].."\\wield "..var["myweapon"])
	else  --smy战斗中另行处理
		local smy_escape=var["smy_escape"] or 1
		local swxy_escape=var["swxy_escape"] or 1
		if smy_escape==1 or swxy_escape==1 then
			exec("job_escape")
		else
			--如果不想跑强力党就捡起武器，继续战斗吧！
			send("set wimpycmd get "..var["smy_weapon"].."\\unwield "..var["weapon"].."\\unwield "..var["myweapon"].."\\wield "..var["smy_weapon"])
			echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."武器掉落，但强力党捡起武器继续战斗！请自己在smy_pfm中增加get weapon;wield weapn功能。")	
		end
	end
end)
-- fight_15 触发器: 武器掉落 (呼呼连响)
-- 当武器“呼呼”连响脱手飞出时触发。逻辑同fight_14。
add_trigger("fight_15","^\\s*只见「呼呼」连响，你.*一个把持不定脱手飞",function(params)
	--send("set wimpycmd halt\\get "..var["myweapon"].."\\unwield "..var["weapon"].."\\unwield "..var["smy_weapon"].."\\wield "..var["myweapon"])
	if var["flag_job"]~="smy" or var["flag_job"]~="swxy" then
		--exec("job_escape")
		send("set wimpycmd halt\\get "..var["myweapon"].."\\unwield "..var["weapon"].."\\unwield "..var["smy_weapon"].."\\wield "..var["myweapon"])
	else  --smy战斗中另行处理
		local smy_escape=var["smy_escape"] or 1
		local swxy_escape=var["swxy_escape"] or 1
		if smy_escape==1 or swxy_escape==1 then
			exec("job_escape")
		else
			--如果不想跑强力党就捡起武器，继续战斗吧！
			send("set wimpycmd get "..var["smy_weapon"].."\\unwield "..var["weapon"].."\\unwield "..var["myweapon"].."\\wield "..var["smy_weapon"])
			echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."武器掉落，但强力党捡起武器继续战斗！请自己在smy_pfm中增加get weapon;wield weapn功能。")	
		end
	end
end)

-- 5) 没装备武器
-- fight_16 触发器: 必须装备暗器/兵器才能使用技能
-- 当出现“必须装备暗器才能使用”、“兵器被”等提示时触发。逻辑同fight_13。
add_trigger("fight_16","^\\s*.*\\(?:必须装备暗器才能使用\\|果你的兵器被\\)",function(params)
	
	if var["flag_job"]~="smy" or var["flag_job"]~="swxy" then
		var["noweaponquit"]=var["noweaponquit"] or 1
		if type(var["noweaponquit"])~="number" or var["noweaponquit"]~=1 then --不quit
			send("alias pfm_backup "..expand(var["pfm6"]))
			send("set wimpy 100")
			send("set wimpycmd hp "..var["char_id"].."\\pfm_backup")
		else --quit
			exec("job_escape")
		end
	else  --smy战斗中另行处理
		local smy_escape=var["smy_escape"] or 1
		local swxy_escape=var["swxy_escape"] or 1
		if smy_escape==1 or swxy_escape==1 then
			exec("job_escape")
		else
			--如果不想跑强力党就捡起武器，继续战斗吧！
			send("set wimpycmd get "..var["smy_weapon"].."\\unwield "..var["weapon"].."\\unwield "..var["myweapon"].."\\wield "..var["smy_weapon"])
			echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."武器彻底没了，但强力党使用备用武器继续战斗！")
			if var["smy_pfm_bk"] and var["smy_pfm_bk"]~="" then --使用自己自带的备份武器，比如商店货
				send("alias smy_pfm_bk "..expand(var["smy_pfm_bk"]))
				send("set wimpy 100")
				send("set wimpycmd hp "..var["char_id"].."\\smy_pfm_bk")
			else --如果没有设置备用武器pfm，那么还是逃跑吧。
				echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."武器彻底没了，而且没有设置颂摩崖备用武器pfm而逃跑。")
				echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."请设置颂摩崖备用武器pfm，配置文件中增加smy_pfm_bk变量！pfm中自己写上武器的wield，pfm对象建议直接xixia wushi 1,xixia wushi 2。")
				exec("job_escape")
			end
		end
	end
end)
-- fight_17 触发器: 必须使用武器/空手才能使用技能
-- 当出现“你手里没”、“你手里无剑”、“你使用的武器不对”等提示时触发。逻辑同fight_13。
add_trigger("fight_17","^\\s*\\(?:你手里没\\|你手里无剑，如何使用\\|你使用的武器不对。\\|你使得了\\|你必须使用武器\\|你必须持剑才能用\\|你不能使用这个绝招\\|你手上没\\|你现在状态不对，无法使用「快剑诀」。\\)",function(params)
	if var["flag_job"]~="smy" or var["flag_job"]~="swxy" then
		var["noweaponquit"]=var["noweaponquit"] or 1
		if type(var["noweaponquit"])~="number" or var["noweaponquit"]~=1 then --不quit
			send("alias pfm_backup "..expand(var["pfm6"]))
			send("set wimpy 100")
			send("set wimpycmd hp "..var["char_id"].."\\pfm_backup")
		else --quit
			exec("job_escape")
		end
	else  --smy战斗中另行处理
		local smy_escape=var["smy_escape"] or 1
		local swxy_escape=var["swxy_escape"] or 1
		if smy_escape==1 or swxy_escape==1 then
			exec("job_escape")
		else
			--如果不想跑强力党就捡起武器，继续战斗吧！
			send("set wimpycmd get "..var["smy_weapon"].."\\unwield "..var["weapon"].."\\unwield "..var["myweapon"].."\\wield "..var["smy_weapon"])
			echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."武器彻底没了，但强力党使用备用武器继续战斗！")
			if var["smy_pfm_bk"] and var["smy_pfm_bk"]~="" then --使用自己自带的备份武器，比如商店货
				send("alias smy_pfm_bk "..expand(var["smy_pfm_bk"]))
				send("set wimpy 100")
				send("set wimpycmd hp "..var["char_id"].."\\smy_pfm_bk")
			else --如果没有设置备用武器pfm，那么还是逃跑吧。
				echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."武器彻底没了，而且没有设置颂摩崖备用武器pfm而逃跑。")
				echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."请设置颂摩崖备用武器pfm，配置文件中增加smy_pfm_bk变量！pfm中自己写上武器的wield，pfm对象建议直接xixia wushi 1,xixia wushi 2。")
				exec("job_escape")
			end
		end
	end
end)

-- 6) 空手装备武器要卸下
-- fight_18 触发器: 手持武器无法使用空手技能 (通用)
-- 当出现“手持武器，如何”等提示时触发。执行一系列unwield命令卸下所有可能的武器。
add_trigger("fight_18","^\\s*你\\(?:手持\\|拿着\\)武器\\(?:，如何\\|怎么能使用\\)",function(params)
	var["other_weapon"]=var["other_weapon"] or ""
	var["myweapon"]=var["myweapon"] or ""
	var["smy_weapon"]=var["smy_weapon"] or ""
	send("set wimpycmd halt\\unwield "..var["myweapon"].."\\unwield "..var["weapon"].."\\unwield "..var["smy_weapon"].."\\unwield "..var["other_weapon"])
end)
-- fight_19 触发器: 手持兵器无法使用“以彼之道 还施彼身”
-- 当出现“你手里拿着兵器，无法使用「以彼之道 还施彼身」绝技。”时触发。逻辑同fight_18。
add_trigger("fight_19","^\\s*你手里拿着兵器，无法使用「以彼之道 还施彼身」绝技。",function(params)
	var["other_weapon"]=var["other_weapon"] or ""
	var["myweapon"]=var["myweapon"] or ""
	var["smy_weapon"]=var["smy_weapon"] or ""
	send("set wimpycmd halt\\unwield "..var["myweapon"].."\\unwield "..var["weapon"].."\\unwield "..var["smy_weapon"].."\\unwield "..var["other_weapon"])
end)
-- fight_20 触发器: 必须空手才能使用技能 (通用)
-- 当出现“先放下手中的武器再说吧？！”或“必须空手才能使用”时触发。逻辑同fight_18。
add_trigger("fight_20","^\\s*你\\(?:先放下手中的武器再说吧？！\\|必须空手才能使用\\)",function(params)
	var["other_weapon"]=var["other_weapon"] or ""
	var["myweapon"]=var["myweapon"] or ""
--	var["changeweapon"]=1
	var["smy_weapon"]=var["smy_weapon"] or ""
	send("set wimpycmd halt\\unwield "..var["myweapon"].."\\unwield "..var["weapon"].."\\unwield "..var["smy_weapon"].."\\unwield "..var["other_weapon"])
end)
-- fight_21 触发器: 只能装备可作武器的东西
-- 当出现“你只能装备可当作武器的东西。”时触发。逻辑同fight_18。
add_trigger("fight_21","^\\s*你只能装备可当作武器的东西。",function(params)
	var["other_weapon"]=var["other_weapon"] or ""
	var["myweapon"]=var["myweapon"] or ""
	var["smy_weapon"]=var["smy_weapon"] or ""
	send("set wimpycmd halt\\unwield "..var["myweapon"].."\\unwield "..var["weapon"].."\\unwield "..var["smy_weapon"].."\\unwield "..var["other_weapon"])
end)

-- 7) 战斗中需要halt（停止当前行动）并恢复状态
-- fight_22 触发器: 混沌剑网消耗精力
-- 当被混沌剑网包裹时触发。执行halt，然后运精力、运力、显示hp。
add_trigger("fight_22","^\\s*\\S+的混沌剑网层层包裹着你，大量消耗你的精力！",function(params)
	send("set wimpycmd halt\\yun jingli\\yun qi\\hp "..var["char_id"].."")
end)
-- fight_23 触发器: 内力不足
-- 当出现“你的内力不”、“你的真气不”等提示时触发。检查是否有川贝丸或大还丹，有则吃药，否则逃跑。
add_trigger("fight_23","^\\s*\\(?:你的内力不\\|你现在内力不\\|你的内力即将用尽\\|你现在内力太弱\\|你现在真气不\\|你的真气不\\|你现在真气太弱\\|你目前内力太少\\|你的内力太少了\\|你内力现在不\\|你目前的内力太少了\\)",function(params)
	send("i")
	item=item or {}
	var["dahuandan"]=var["dahuandan"] or 0
	if item["chuanbei wan"] and item["chuanbei wan"]>0 then --带了川贝丸
		send("set wimpy 100")
		send("set wimpycmd eat chuanbei wan\\hp "..var["char_id"].."\\pfm")
		send("pfm")
	elseif var["dahuandan"]>0 and (item["dahuan dan"]>0 or item["da huandan"]>0) then --带了大还丹
		send("set wimpy 100")
		send("set wimpycmd fu dan\\hp "..var["char_id"]..";i")
		--send("pfm")
	else
		exec("job_escape")
	end
end)
-- fight_24 触发器: 身法不足
-- 当出现“你的身法不够！”或“你的身法不足，无法使用”时触发。如果不是smy/swxy任务，则执行halt;yun qi;hp；否则运力。
add_trigger("fight_24","^\\s*\\(?:你的身法不够！\\|你的身法不足，无法使用\\)",function(params)
	
	if var["flag_job"]~="smy" or var["flag_job"]~="swxy" then
		send("set wimpycmd halt\\yun qi\\hp "..var["char_id"].."")
	else  --smy战斗中另行处理
		local smy_escape=var["smy_escape"] or 1
		local swxy_escape=var["swxy_escape"] or 1
		if smy_escape==1 or swxy_escape==1 then
			exec("job_escape")
		else
			send("yun qi")
		end
	end
end)
-- fight_25 触发器: 体力快消耗完了
-- 当出现“你的体力快消耗完了！”时触发。如果不是smy/swxy任务，则执行halt;yun jingli;yun qi;hp；否则运精力。
add_trigger("fight_25","^\\s*你的体力快消耗完了！",function(params)
	
	if var["flag_job"]~="smy" or var["flag_job"]~="swxy" then
		send("set wimpycmd halt\\yun jingli\\yun qi\\hp "..var["char_id"].."")
	else  --smy战斗中另行处理
		local smy_escape=var["smy_escape"] or 1
		local swxy_escape=var["swxy_escape"] or 1
		if smy_escape==1 or swxy_escape==1 then
			exec("job_escape")
		else
			send("yun jingli")
		end
	end
end)

-- var["fight_pause"]=0
-- var["fight_quit"]=0
-- var["fight_win"]=0

-- fight_26 触发器: 玩家跳出战圈
-- 当玩家身形后跃，跳出战圈时触发。根据fight_win、fight_quit、xueshan_poison_kill_halt或fight_pause的状态执行相应逻辑。
add_trigger("fight_26","^\\s*你身形向后一跃，跳出战圈不打了。",function(params)
	var["fight_win"]=var["fight_win"] or 0
	var["fight_pause"]=var["fight_pause"] or 0
	var["fight_quit"]=var["fight_quit"] or 0
	local dir=get_random_move(var["roomexit"]) --方向
	local reverse_dir=get_reverse_move(dir) --反方向
	if var["xueshan_dir"]==nil then --如果没有雪山的逃跑房间就设置一下
		var["xueshan_dir"]=dir
		var["xueshan_reverse_dir"]=reverse_dir
	end
	
	local pfm_id=var["pfm_id"] or ""
	if var["fight_escape"] and var["fight_escape"]==1 then
	
	elseif var["fight_win"]~=0 then -- fight_win 战斗结束参数
		exec("kill_job_npc")
	elseif var["fight_quit"]~=0 then --fight quit 逃跑
		exec("job_escape")
	elseif var["xueshan_poison_kill_halt"]~=nil then
		var["xueshan_poison_kill_halt"]=nil
		exec(var["xueshan_dir"]..";look;fight_reset;alias action 保镖已经中毒静候佳音...")
	elseif var["fight_pause"]==0 then -- fight pause=0 没有暂停战斗！！！
		if var["flag_job"]~="smy" or var["flag_job"]~="swxy" then
			send("follow "..pfm_id)
			send("set wimpy 100")
			exec("kill_job_npc")
			send("fight_reset")--战斗设置复位 使用pfm_backup
		elseif var["flag_job"]=="smy" or var["flag_job"]=="swxy" then
			echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."战斗中halt了一下！")
		end
	end
end)

-- 8) cond nopfm (状态异常，无法施展武功)
-- fight_27 触发器: 气息不匀，无法施展外功 (封招)
-- 当出现“你气息不匀，暂时不能施用外功”时触发。如果不是smy/swxy任务，则暂停战斗并尝试恢复；否则根据smy_escape/swxy_escape决定是否逃跑，或尝试用特定pfm解除封招。
add_trigger("fight_27","^\\s*\\(\\s*你气息不匀，暂时不能施用外功",function(params)
	if var["flag_job"]~="smy" or var["flag_job"]~="swxy" then
		var["dazhuan"]=var["dazhuan"] or 0
		if var["dazhuan"]~=0 then
			var["dazhuan"]=0
		else
			if var["combat_force_escape"]==nil or var["combat_force_escape"]==0 then
				var["fight_pause"]=1 --战斗暂停
				send("set wimpy 100")
				send("set wimpycmd halt\\yun qi")
				set_timer("timer",1,function()
					exec("no_pfm")--定时器执行no_pfm
				end)
			else
				exec("job_escape")
			end
		end
	else  --smy战斗中另行处理
		var["dazhuan"]=var["dazhuan"] or 0
		if var["dazhuan"]~=0 then
			var["dazhuan"]=0
		else
			local smy_escape=var["smy_escape"] or 1
			local swxy_escape=var["swxy_escape"] or 1
			if smy_escape==1 or swxy_escape==1 then -- 修正 swxy==1 为 swxy_escape==1
				exec("job_escape")
			else
				--如果不想跑强力党就硬挺吧！
				if var["smy_fengzhao_yun"] and var["smy_fengzhao_yun"]~="" then
						local fengzhao_yun=get_first_pfm(var["smy_fengzhao_yun"]) --封招可以yun的pfm
						exec(fengzhao_yun) --封招可以yun
				end
				if var["swxy_fengzhao_yun"] and var["swxy_fengzhao_yun"]~="" then
						local fengzhao_yun=get_first_pfm(var["swxy_fengzhao_yun"]) --封招可以yun的pfm
						exec(fengzhao_yun) --封招可以yun
				end
				
				echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."遇到封招，但强力党继续战斗！")	
			end
		end
	end
end)
-- fight_28 触发器: 全身穴道受阻，内劲无法控制 (闭气)
-- 当出现“你只觉得全身周遭穴道受阻，不由精神恍惚，只觉得内劲无法控制。”时触发。逻辑同fight_27。
add_trigger("fight_28","^\\s*\\(?:xxxx你气息不匀，暂时不能施用内功。\\|你只觉得全身周遭穴道受阻，不由精神恍惚，只觉得内劲无法控制。\\)",function(params)
	if var["flag_job"]~="smy" or var["flag_job"]~="swxy" then
		var["dazhuan"]=var["dazhuan"] or 0
		if var["dazhuan"]~=0 then
			var["dazhuan"]=0
		else
			if var["combat_force_escape"]==nil or var["combat_force_escape"]==0 then
				var["fight_pause"]=1 --战斗暂停
				send("set wimpy 100")
				send("set wimpycmd halt\\yun qi")
				set_timer("timer",1,function()
					exec("no_pfm")--定时器执行no_pfm
				end)
			else
				exec("job_escape")
			end
		end
	else  --smy战斗中另行处理
		var["dazhuan"]=var["dazhuan"] or 0
		if var["dazhuan"]~=0 then
			var["dazhuan"]=0
		else
			local smy_escape=var["smy_escape"] or 1
			local swxy_escape=var["swxy_escape"] or 1
			if smy_escape==1 or swxy_escape==1 then -- 修正 swxy_excape==1 为 swxy_escape==1
				exec("job_escape")
			else
				--如果不想跑强力党就硬挺吧！
				echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."遇到闭气，但强力党继续战斗！")	
			end
		end
	end
end)

-- 9) 受伤
-- fight_29 触发器: 玩家受伤状态
-- 当出现“你已经一副头重脚轻的模样”、“已经陷入半昏迷状态”等提示时触发。
-- 检查是否有大还丹，有则吃药；否则尝试运力恢复，或根据job类型决定是否halt。
add_trigger("fight_29","^\\s*\\(\\s*你\\(?:已经一副头重脚轻的模样\\|已经陷入半昏迷状态\\|摇头晃脑、歪歪斜斜\\|似乎十分疲惫\\|看起来已经力不从心\\)",function(params)
	send("i")
	item=item or {}
	var["dahuandan"]=var["dahuandan"] or 0
	var["fight_quit"]=var["fight_quit"] or 0
	if var["dahuandan"]>0 and (item["dahuan dan"]>0 or item["da huandan"]>0) then --带了大还丹
		if var["flag_job"]=="smy" or var["flag_job"]=="swxy" then  --只有颂摩崖，才吃大还丹
			send("set wimpy 100")
			send("set wimpycmd halt\\fu dan\\hp "..var["char_id"]..";i")
		else
			send("set wimpy 100")
			send("set wimpycmd halt\\fu dan\\hp "..var["char_id"]..";i")
		end
	else
		if var["combat_no_refresh"]==nil or var["combat_no_refresh"]==0 then
			send("yun qi")
			wait(0.2,function() --等待0.2s以后再yun qi
				send("yun qi")
			end)
			if var["fight_quit"]==0 then
				if var["flag_job"]~="smy" then --非颂摩崖可以halt，颂摩崖halt就是找死，找发呆。
					send("set wimpy 100")
					send("set wimpycmd halt\\yun qi\\hp "..var["char_id"].."")
				end
				if var["flag_job"]~="swxy" then --非
					send("set wimpy 100")
					send("set wimpycmd halt\\yun qi\\hp")
				end
				
			end
		elseif  var["combat_no_refresh"]==1 then 
			send("yun qi")
			wait(0.2,function() --等待0.2s以后再yun qi
				send("yun qi")
			end)
	
		
		end
	end
end)

-- 10) HP/MP状态监控
-- fight_30 触发器: 精血/精力状态监控
-- 捕捉精血和精力状态信息。根据精血百分比决定是否逃跑，或运精/运精力。
add_trigger("fight_30","^\\s*\\·精血\\·\\s*(\\d+)\\s*/\\s*(\\d+)\\s*\\(\\s*(\\d+)%\\)\\s+\\·精力\\·\\s*(\\d+)\\s*/\\s*(\\d+)\\(",function (params) 
var["jing"],var["maxjing"],var["hurtjing"]=tonumber(params[1]),tonumber(params[2]),tonumber(params[3])
var["jingli"],var["maxjingli"]=tonumber(params[4]),tonumber(params[5])
	var["fight_win"]=var["fight_win"] or 0
	if var["fight_win"]==0 then -- fight_win 战斗结束参数
		if var["hurtjing"]<60 then
			exec("job_escape")
		elseif var["jing"]<var["maxjing"] and var["jing"]<var["maxjing"]*100/var["hurtjing"]/2 then
			send("yun jing")
		elseif var["jingli"]<var["maxjingli"]*3/5 then
			send("yun jingli")
		end
	end
end
)
-- fight_31 触发器: 气血/内力状态监控
-- 捕捉气血和内力状态信息。根据气血百分比决定是否吃大还丹或逃跑，或运力。
add_trigger("fight_31","^\\s*\\·气血\\·\\s*(\\d+)\\s*/\\s*(\\d+)\\s*\\(\\s*(\\d+)%\\)\\s+\\·内力\\·\\s*(\\d+)\\s*/\\s*(\\d+)\\(\\+(\\d+)",function (params) 
var["qi"],var["maxqi"],var["hurtqi"]=tonumber(params[1]),tonumber(params[2]),tonumber(params[3])
var["neili"],var["maxneili"],var["jiali"]=tonumber(params[4]),tonumber(params[5]),tonumber(params[6])
	var["fight_win"]=var["fight_win"] or 0
	item=item or {}
	var["dahuandan"]=var["dahuandan"] or 0		
	if var["fight_win"]==0 then -- fight_win 战斗结束参数
		if var["hurtqi"]<60 then
				if var["dahuandan"]>0 and item["dahuan dan"] and item["dahuan dan"]>0 then --带了大还丹
					send("set wimpy 100")
					send("set wimpycmd halt\\fu dan\\hp "..var["char_id"]..";i")
				elseif var["dahuandan"]>0 and item["da huandan"] and item["da huandan"]>0 then --带了大还丹
					send("set wimpy 100")
					send("set wimpycmd halt\\fu dan\\hp "..var["char_id"]..";i")
				else
					exec("job_escape")
				end
		elseif var["qi"]<var["maxqi"]*100/5/var["hurtqi"] and var["combat_no_refresh"]~=nil and var["combat_no_refresh"]==2 then
			exec("job_escape")
		elseif var["qi"]<var["maxqi"] and var["qi"]<var["maxqi"]*100*2/4/var["hurtqi"] then
			if var["combat_no_refresh"]==nil or var["combat_no_refresh"]==0 or var["combat_no_refresh"]==1 then
				send("yun qi")
				wait(0.4,function()
				send("yun qi")
				end)			
			end
		end
	end
end)
-- fight_46 触发器: 新的机器人hp id (通用HP状态监控)
-- 捕捉更详细的HP状态信息。根据hurtqi百分比决定是否吃大还丹或逃跑，或运力。
add_trigger("fight_46","^\\s*HP\\:(.+)",function (params)
	send("i") 
	local jing,maxjing,hurtjing,jingli,maxjingli,qi,maxqi,hurtqi,neili,maxneili,jiali,shen,food,pot,maxpot,water,myexp=string.match(params[1],
	"(.%d*)/(%d-)/(%d-)%%/(.%d*)/(%d-)/%d-/(.%d*)/(%d-)/(%d-)%%/(.%d*)/(%d-)/%+(%d-)/(.%d-)/%d-/%d-/(%d-)%.%d%d%%/(%d-)/(%d-)/(%d-)%.%d%d%%/(%d-)/%d")

	var["jing"],var["maxjing"],var["hurtjing"],var["jingli"],var["maxjingli"],var["qi"],var["maxqi"],var["hurtqi"],var["neili"],var["maxneili"],var["jiali"],var["shen"],var["food"],var["pot"],var["maxpot"],var["water"],var["exp"]=tonumber(jing),tonumber(maxjing),tonumber(hurtjing),tonumber(jingli),tonumber(maxjingli),tonumber(qi),tonumber(maxqi),tonumber(hurtqi),tonumber(neili),tonumber(maxneili),tonumber(jiali),tonumber(shen),tonumber(food),tonumber(pot),tonumber(maxpot),tonumber(water),tonumber(myexp)
	jing,maxjing,hurtjing,jingli,maxjingli,   qi,maxqi,hurtqi,neili,maxneili,   jiali,shen,food,pot,maxpot, water,myexp=nil,nil,nil,nil,nil, nil,nil,nil,nil,nil, nil,nil,nil,nil,nil ,nil,nil
	
--var["qi"],var["maxqi"],var["hurtqi"]=tonumber(params[1]),tonumber(params[2]),tonumber(params[3])
--var["neili"],var["maxneili"],var["jiali"]=tonumber(params[4]),tonumber(params[5]),tonumber(params[6])
	var["fight_win"]=var["fight_win"] or 0
	item=item or {}
	var["dahuandan"]=var["dahuandan"] or 0		
	if var["fight_win"]==0 then -- fight_win 战斗结束参数
		if var["hurtqi"]<10 then
				if var["dahuandan"]>0 and item["dahuan dan"] and item["dahuan dan"]>0 then --带了大还丹
					send("set wimpy 100")
					send("set wimpycmd halt\\fu dan\\hp "..var["char_id"]..";i")
				elseif var["dahuandan"]>0 and item["da huandan"] and item["da huandan"]>0 then --带了大还丹
					send("set wimpy 100")
					send("set wimpycmd halt\\fu dan\\hp "..var["char_id"]..";i")
				else
					exec("fu dan")
				end
		elseif var["qi"]<var["maxqi"]*100/5/var["hurtqi"] and var["combat_no_refresh"]~=nil and var["combat_no_refresh"]==2 then
			exec("job_escape")
		elseif var["qi"]<var["maxqi"] and var["qi"]<var["maxqi"]*100*2/4/var["hurtqi"] then
			if var["combat_no_refresh"]==nil or var["combat_no_refresh"]==0 or var["combat_no_refresh"]==1 then
				send("yun qi")
				wait(0.4,function()
				send("yun qi")
				end)			
			end
		end
	end
end)
-- 11) 大轮寺dazhuan (大转盘相关)
-- fight_32 触发器: 大转盘开始
-- 当大转盘金光闪闪、银光烁烁时触发。设置use_dazhuan和dazhuan为1，并设置pfm别名和wimpycmd。
add_trigger("fight_32","^\\s*你纵跃退後，立时呜呜、嗡嗡、轰轰之声大作，金光闪闪，银光烁烁",function(params)
	var["use_dazhuan"]=1 --此时强制参数use_dazhuan 1
	
	var["dazhuan"]=1
	var["pfm6"]=var["pfm6"] or ""
	var["myweapon"]=var["myweapon"] or ""
	send("alias pfm "..expand(var["pfm6"]))
	send("set wimpycmd hp "..var["char_id"].."\\pfm")
	send("alias wield_weapon unwield "..var["myweapon"])
end)
-- fight_33 触发器: 大转盘武器飞回
-- 当飞行中的武器飞回手中时触发。跟随pfm_id，装备武器，击杀npc，并复位战斗设置。
add_trigger("fight_33","^\\s*.*在.*身旁绕了个圈子，.*伸手一招，那飞行中的.*便重新飞回你的手中！",function(params)

local pfm_id=var["pfm_id"] or ""
		send("follow "..pfm_id)
		send("set wimpy 100")
		send("wield "..var["myweapon"])
		exec("kill_job_npc")
		send("fight_reset") --复位

end)
-- fight_34 触发器: 摄心大法影响
-- 当敌人受到摄心大法影响，斗志消失时触发。跟随pfm_id，击杀npc。
add_trigger("fight_34","^\\s*结果.*受到你的摄心大法的影响，原先的斗志顿然消失！",function(params)
	send("follow "..var["pfm_id"])
	exec("kill_job_npc")
end)
-- fight_35 触发器: 换pfm (沮丧)
-- 当玩家沮丧地发现无法使用当前pfm时触发。切换到pfm6。
add_trigger("fight_35","^\\s*但是你沮丧地发觉",function(params)
	var["pfm6"]=var["pfm6"] or ""
	send("alias pfm "..expand(var["pfm6"]))
	send("set wimpycmd hp "..var["char_id"].."\\pfm")
end)
-- fight_36 触发器: 抛出火焰
-- 当玩家口吐青烟，抛出火焰时触发。跟随pfm_id，击杀npc。
add_trigger("fight_36","^\\s*你口中吐出一口青烟，将手中.*用内力一搓一抛，轰地一声燃起一堆熊熊火焰！",function(params)
	send("follow "..var["pfm_id"])
	send("set wimpy 100")
	exec("kill_job_npc")
end)
-- fight_37 触发器: 未运无敌无法使用摄心
-- 当出现“你没有运无敌怎么能使用「摄心」？！”时触发。执行halt;yun wudi;yun qi。
add_trigger("fight_37","^\\s*你没有运无敌怎么能使用「摄心」？！",function(params)
	send("set wimpy 100")
	send("set wimpycmd halt\\yun wudi\\yun qi")
end)
-- fight_38 触发器: 一阳指柔劲吸收
-- 当玩家使用一阳指柔劲吸收时触发。根据吸收次数决定是否切换pfm6。
add_trigger("fight_38","^\\s*你以一阳指柔劲吸收掉",function(params)
	var["rou"]=var["rou"] or 0
	var["rou"]=var["rou"]+1
	if var["rou"]>4 then
		var["rou"]=0
		var["pfm6"]=var["pfm6"] or ""
		send("alias pfm "..expand(var["pfm6"]))
		send("set wimpycmd hp "..var["char_id"].."\\pfm")
	end
end)
-- fight_39 触发器: NPC状态变化 (如中毒)
-- 当NPC脸上紫气一闪或出掌速度变慢时触发。判断是否是任务NPC，若是则复位dazhuan，跟随pfm_id，击杀npc。
add_trigger("fight_39","^\\s*(\\S{4,16})\\(?:脸上紫气一闪，又恢复如常。\\|出掌的速度慢了下来！\\)",function(params)
	local npc_name=params[1]
	var["dazhuan"]=var["dazhuan"] or 0
	var["job_npc_name"]=var["job_npc_name"] or "" --任务npc
	var["danger_name"]=var["danger_name"] or "" --危险npc
	var["killer1_name"]=var["killer1_name"] or "" --killer1
	var["killer_name"]=var["killer_name"] or ""
	if string.find(var["job_npc_name"],npc_name) or string.find(var["danger_name"],npc_name) or string.find(var["killer1_name"],npc_name) or string.find(var["killer_name"],npc_name) then
		if var["dazhuan"]==1 then var["dazhuan"]=0 end
		send("follow "..var["pfm_id"])
		exec("kill_job_npc")
		send("set wimpy 100")
	end
end)
-- fight_40 触发器: 夺取敌人武器
-- 当成功夺取敌人武器时触发。如果需要，切换pfm6。
add_trigger("fight_40","^\\s*\\S{4,16}手中的.*已被你硬生生的夺去了！",function(params)
	if 1==0 then	--要换么
		var["pfm6"]=var["pfm6"] or ""
		var["myweapon"]=var["myweapon"] or ""
		send("alias pfm "..expand(var["pfm6"]))
		send("set wimpycmd hp "..var["char_id"].."\\pfm")
	end
end)
-- fight_41 触发器: 敌人被克制
-- 当敌人处处受制，武功无法发挥时触发。如果需要，切换pfm6。
add_trigger("fight_41","^\\s*(\\S+)\\(?:只觉得处处受制，武功中厉害之处完全无法发挥出来！\\)",function(params)
	local killer_name=var["killer_name"] or "none"
	local job_npc_name=var["job_npc_name"] or "none"
	local npc_name=killer_name..job_npc_name
	if 1==0 and string.find(npc_name,params[1]) then --9jian 中了要换pfm6么?
		var["pfm6"]=var["pfm6"] or ""
		send("alias pfm "..expand(var["pfm6"]))
		send("set wimpycmd hp "..var["char_id"].."\\pfm")
	end
end)
-- fight_42 触发器: 敌人施展金光闪闪技能
-- 当敌人施展金光闪闪技能时触发。如果敌人是任务NPC，则执行halt;yun qi;hp。
add_trigger("fight_42","^\\s*(\\S+)纵跃退後，立时呜呜、嗡嗡、轰轰之声大作，金光闪闪",function(params)
	local killer_name=var["killer_name"] or "none"
	local job_npc_name=var["job_npc_name"] or "none"
	local danger_name=var["danger_name"] or "none"
	local npc_name=killer_name..job_npc_name..danger_name
	if string.find(npc_name,params[1]) then
		send("set wimpycmd halt\\yun qi\\hp "..var["char_id"].."")
	end
end)
-- fight_43 触发器: action设定为“暂停战斗，检查个人状态”
-- 当action设置为“暂停战斗，检查个人状态”时触发。根据战斗状态、中毒状态、气血/大还丹情况执行相应逻辑。
add_trigger("fight_43","^\\s*你把\\s+\"action\"\\s+设定为\\s+\"暂停战斗，检查个人状态",function(params)
del_timer("input")
	var["fight_win"]=var["fight_win"] or 0
	var["fight_pause"]=var["fight_pause"] or 0
	var["fight_quit"]=var["fight_quit"] or 0
	item=item or {}
	var["dahuandan"]=var["dahuandan"] or 0
	echo("\n"..C.W..var["cond"]..""..var["fight_pause"])
	if var["fight_win"]~=0 then -- fight_win 战斗结束参数
		exec("kill_job_npc")
	elseif var["fight_quit"]~=0 then --fight quit 逃跑
		exec("job_escape")
	elseif var["cond"]==2 then --中毒？
		exec("job_escape")
	--elseif var["cond"]==1 and item["田七鲨胆散"]>0 then --中了封招等？
	--	exec("eat tianqi")	
	elseif var["hurtqi"]<70 and var["dahuandan"]>0 and (item["dahuan dan"] or item["da huandan"]) then --嗑药？
		unset_timer("timer")
		send("set wimpy 100")
		send("set wimpycmd halt\\fu dan\\hp "..var["char_id"]..";i")
	elseif var["cond"] and var["cond"]==0 then
		unset_timer("timer")
		var["fight_pause"]=0
		exec("wield_weapon")
		exec("kill_job_npc")
		send("set wimpy 100")
		if var["flag_job"]=="quest" then
			send("set wimpycmd pfm\\hp "..var["char_id"].."")
		else
			send("set wimpycmd pfm_backup\\hp "..var["char_id"].."")
		end
		wait(5,function()
			exec("kill_job_npc")
		end)
	end
end)
-- fight_44 触发器: action设定为“战斗失败，立即撤退”
-- 当action设置为“战斗失败，立即撤退”时触发。根据提示信息决定是任务失败、逃跑，还是继续尝试。
add_trigger("fight_44","^\\s*你把\\s+\"action\"\\s+设定为\\s+\"战斗失败，立即撤退",function(params)
	del_timer("input")
	if string.find(line[2],"这里没有") then
		var["job_step"]="escape"
		var["do_stop"]=0
		unset_timer("timer")
		unset_timer("wait")
		close_fight()
		if var["flag_job"]=="smy" then
			close_smy()
		end
		if var["flag_job"]=="swxy" then
			close_swxy()
		end
		var["fight_escape"]=0
		close_trigger("fight_44")
		if var["log_fail_fight"]==nil then --还没记录过了
			var["log_fail_fight"]="done"
			do_log("fail_fight")
		end
		wait(1.5,function()
			var["do_stop"]=0
			exec("check_poison job_fail")
		end)
	elseif string.find(line[2],"你必须等对方同意才能领着对方走") or string.find(line[2],"已经跟着你") then
		wait(0.6,function()
		send("halt")
		exec("random_move")
		send("look")
		--send("lead "..var["pfm_id"])
		--send("alias action 战斗失败，立即撤退...")
		exec("job_escape")
		end)
	end

end)
-- fight_45 触发器: 禁止战斗区域
-- 当出现“这里不准战斗。”、“这里禁止战斗。”等提示时触发。
-- 根据job_no_fight计数决定是任务失败还是尝试移动并攻击。
add_trigger("fight_45","^\\s*\\(?:这里不准战斗。\\|这里禁止战斗。\\|你正要有所动作，突然身旁有人将你一拍：好好看比武，别乱动！\\)",function(params)
--	var["do_stop"]=1
	close_trigger("fight_45")
	var["idle"]=0
	local job_no_fight=var["job_no_fight"] or 0
	if job_no_fight>50 then
		var["do_stop"]=0
		exec("check_poison job_fail")
	else
		var["job_no_fight"]=job_no_fight+1
		alarm("no_fight",1,function()
			open_trigger("fight_45")
			local dir=get_random_move(var["roomexit"])
			local reverse_dir=get_reverse_move(dir)
			send(dir)
			send(reverse_dir)
--			send("look")
			exec("look;kick @pfm_id;kill_job_npc")
--			exec("random_move;look;kill @pfm_id")
		end)
	
	end
end)

--> 你转身就要开溜，被金郭凤金一把拦住！
--你逃跑失败。
--add_trigger("fight_common")

--^?????这里没有 @pfm_id。$*action*Job fail
--^???你把 "action" 设定为 "Check Status No PFM" 成功完成。
--^???你必须等对方同意才能领着对方走。$*action*Job fail

-- alias 部分
-- no_pfm 别名: 暂停战斗，检查状态，并运力
-- 开启cond相关触发器，设置fight_pause为1，执行halt;yun qi;cond;hp命令。
add_alias("no_pfm",function(params)
open_trigger("cond_1")
open_trigger("cond_2")
open_trigger("cond_3")
open_trigger("cond_4")
var["fight_pause"]=1
--var["cond"]=0
var["idle"]=0
exec("halt;yun qi;cond;hp "..var["char_id"]..";alias action 暂停战斗，检查个人状态...")
--send("halt")
--send("yun qi")
--send("cond")
--send("hp")
--send("alias action 暂停战斗，检查个人状态...")
end)

-- random_move 别名: 随机移动
-- 获取一个随机方向并发送移动命令。
add_alias("random_move",function(params)
local dir=get_random_move(var["roomexit"])
send(dir)
end)

-- 函数部分
-- set_fight 函数: 设置战斗模式和相关参数
-- 根据传入的job参数，设置战斗状态、Pfm别名、wimpycmd等。
function set_fight(job)
var["idle"]=0--不发呆
close_crush()--关闭杀拦路人触发
unset_timer("timer")
unset_timer("check_busy_1")
unset_timer("check_busy_2")
close_trigger("walk_1")
close_trigger("walk_2")
close_trigger("walk_3")
del_timer("do_walk")
close_trigger("check_busy_1")
close_trigger("check_busy_2")

if job=="sx_kill_killer" then
	var["fight"]=1
	send("alias pfm "..expand(var["pfm1"]))
	send(expand(var["pfm1"]))
	send("alias pfm_backup "..expand(var["pfm1"]))
	send("set wimpy 100")
	send("set wimpycmd hp "..var["char_id"].."\\pfm")
elseif job=="xs_kill_guard" then
	var["fight"]=1
--local first_pfm=var["first_pfm"] or ""
	send("alias pfm "..expand(var["pfm2"]))
--	send(expand(var["pfm1"]))
	send("alias pfm_backup "..expand(var["pfm2"]))
--	send("set wimpy 100")
	send("set wimpycmd hp "..var["char_id"].."\\pfm")
else
	var["do_stop"]=1
	var["fight_win"]=0
	var["fight"]=1
	var["idle"]=0
	var["time_begin"]=os.time()
	open_fight()
	exec("wield_weapon")
	send("follow "..var["pfm_id"])
	if job=="run" then
	--send("set wimpy 100")
	--send("set wimpycmd halt\\yun qi\\hp")
	elseif job=="hs1" then
	send("alias pfm "..expand(var["pfm_hs"]))
--	send(expand(var["pfm_hs"]))
	send("alias pfm_backup "..expand(var["pfm_hs"]))
	send("set wimpy 100")
	send("set wimpycmd hp "..var["char_id"].."\\pfm")
	elseif job=="gb" then
	send("alias pfm "..expand(var["pfm2"]))
--	exec(expand(var["pfm2"]))
	send("alias pfm_backup "..expand(var["pfm2"]))
	send("set wimpy 100")
	send("set wimpycmd hp "..var["char_id"].."\\pfm")
	elseif job=="tdh" then
	send("alias pfm "..expand(var["pfm_tdh"]))
--	exec(expand(var["pfm_tdh"]))
	send("alias pfm_backup "..expand(var["pfm_tdh"]))
	send("set wimpy 100")
	send("set wimpycmd hp "..var["char_id"].."\\pfm")
	elseif job=="smy" then
	send("alias pfm "..expand(var["smy_pfm"]))
--	exec(expand(var["smy_pfm"]))
	send("alias pfm_backup "..expand(var["smy_pfm"]))
	send("set wimpy 100")
	send("set wimpycmd hp "..var["char_id"].."\\pfm")
	elseif job=="swxy" then
	send("alias pfm "..expand(var["swxy_pfm"]))
--	exec(expand(var["pfm2"]))
	send("alias pfm_backup "..expand(var["swxy_pfm"]))
	send("set wimpy 100")
	send("set wimpycmd hp "..var["char_id"].."\\pfm")
	elseif job=="quest" then
	--send("alias pfm "..expand(var["pfm_quest"]))
	--send("alias pfm_backup "..expand(var["pfm_quest"]))
	send("set wimpy 100")
	send("set wimpycmd hp "..var["char_id"].."\\pfm")
	elseif job=="ss" then
	local ss=var["songshan_type"] or "none"
	if ss=="请" and var["pfm_qing"] and var["pfm_qing"]~="" then
		send("alias pfm "..expand(var["pfm_qing"]))
--	send(expand(var["pfm_qing"]))
	send("alias pfm_backup "..expand(var["pfm_qing"]))
	else
	send("alias pfm "..expand(var["pfm2"]))
--	send(expand(var["pfm2"]))
	send("alias pfm_backup "..expand(var["pfm2"]))
	end
	send("set wimpy 100")
	send("set wimpycmd hp "..var["char_id"].."\\pfm")
	elseif job=="sx1" or job=="xl" or job=="zs" then
	send("alias pfm "..expand(var["pfm1"]))
--	send(expand(var["pfm1"]))
	send("alias pfm_backup "..expand(var["pfm1"]))
	send("set wimpy 100")
	send("set wimpycmd hp "..var["char_id"].."\\pfm")
	elseif job=="hs2" or job=="dohs2" then
	send("alias pfm "..expand(var["pfm_hs"]))
--	send(expand(var["pfm_hs"]))
	send("alias pfm_backup "..expand(var["pfm_hs"]))
	send("set wimpy 100")
	send("set wimpycmd hp "..var["char_id"].."\\pfm")
	elseif job=="cisha" then
	send("alias pfm "..expand(var["pfm2"]))
--	send(expand(var["pfm2"]))
	send("alias pfm_backup "..expand(var["pfm2"]))
	send("set wimpy 100")
	send("set wimpycmd hp "..var["char_id"].."\\pfm")
	elseif job=="clb2" or job=="clb" then
	send("alias pfm "..expand(var["pfm2"]))
--	send(expand(var["pfm2"]))
	send("alias pfm_backup "..expand(var["pfm2"]))
	send("set wimpy 100")
	send("set wimpycmd hp "..var["char_id"].."\\pfm")
	elseif job=="wd" or job=="sx2" then
	set_special_pfm()
	send("set wimpy 100")
	send("set wimpycmd hp "..var["char_id"].."\\pfm")
	elseif job=="gf" then
	set_special_pfm()
	send("set wimpy 100")
	send("set wimpycmd hp "..var["char_id"].."\\pfm")
	elseif job=="xs" then
		local xueshan_job=var["xueshan_job"] or 1
		if xueshan_job==2 then
--			exec("kill @pfm_id")
		else	
--			exec("fight @pfm_id")
		end
		set_special_pfm()
--		echo("\n"..C.m..type(var["time_begin"]))
		send("set wimpy 100")
		send("set wimpycmd hp "..var["char_id"].."\\pfm")
	else
	send("alias pfm "..expand(var["pfm2"]))
	send("alias pfm_backup "..expand(var["pfm2"]))
	send("set wimpy 100")
	send("set wimpycmd hp "..var["char_id"].."\\pfm")
	end

end
end
-- set_special_pfm 函数: 根据敌人技能和门派动态设置Pfm
-- 根据敌人的技能、门派、是否为大内高手等信息，动态选择合适的pfm（perform命令）并设置pfm别名和备份pfm。
function set_special_pfm()--设置根据对手skills 门派的pfm
	local skill=var["killer_skill"] or "none"
	local party=var["killer_party"] or "none"
	local killer_super=var["killer_super"] or 0
	local super,pfm="普通低手",var["pfm2"]
	if killer_super==1 then
		super="大内高手"
	end
	--门派
	local check=var["party_special_1"]..var["party_special_2"]..var["party_special_3"]..var["party_special_4"]..var["party_special_5"]..var["party_special_6"]
	if check~="" then
		if string.find(var["party_special_1"],party) then pfm=var["pfm_party_1"] end
		if string.find(var["party_special_2"],party) then pfm=var["pfm_party_2"] end
		if string.find(var["party_special_3"],party) then pfm=var["pfm_party_3"] end
		if string.find(var["party_special_4"],party) then pfm=var["pfm_party_4"] end
		if string.find(var["party_special_5"],party) then pfm=var["pfm_party_5"] end
		if string.find(var["party_special_6"],party) then pfm=var["pfm_party_6"] end
	end
	--技能
	local check=var["skill_special_1"]..var["skill_special_2"]..var["skill_special_3"]..var["skill_special_4"]..var["skill_special_5"]..var["skill_special_6"]..var["skill_special_7"]..var["skill_special_8"]..var["skill_special_9"]..var["skill_special_10"]..var["skill_special_11"]..var["skill_special_12"]
	if check~="" then
		if string.find(var["skill_special_1"],skill) then pfm=var["pfm_skill_1"] end
		if string.find(var["skill_special_2"],skill) then pfm=var["pfm_skill_2"] end
		if string.find(var["skill_special_3"],skill) then pfm=var["pfm_skill_3"] end
		if string.find(var["skill_special_4"],skill) then pfm=var["pfm_skill_4"] end
		if string.find(var["skill_special_5"],skill) then pfm=var["pfm_skill_5"] end
		if string.find(var["skill_special_6"],skill) then pfm=var["pfm_skill_6"] end
		if string.find(var["skill_special_7"],skill) then pfm=var["pfm_skill_7"] end
		if string.find(var["skill_special_8"],skill) then pfm=var["pfm_skill_8"] end
		if string.find(var["skill_special_9"],skill) then pfm=var["pfm_skill_9"] end
		if string.find(var["skill_special_10"],skill) then pfm=var["pfm_skill_10"] end
		if string.find(var["skill_special_11"],skill) then pfm=var["pfm_skill_11"] end
		if string.find(var["skill_special_12"],skill) then pfm=var["pfm_skill_12"] end
	end
	--门派+技能
	local check=var["party_skill_special_1"]..var["party_skill_special_2"]..var["party_skill_special_3"]..var["party_skill_special_4"]..var["party_skill_special_5"]..var["party_skill_special_6"]
	if check~="" then
		if string.find(var["party_skill_special_1"],party..skill) then pfm=var["pfm_party_skill_1"] end
		if string.find(var["party_skill_special_2"],party..skill) then pfm=var["pfm_party_skill_2"] end
		if string.find(var["party_skill_special_3"],party..skill) then pfm=var["pfm_party_skill_3"] end
		if string.find(var["party_skill_special_4"],party..skill) then pfm=var["pfm_party_skill_4"] end
		if string.find(var["party_skill_special_5"],party..skill) then pfm=var["pfm_party_skill_5"] end
		if string.find(var["party_skill_special_6"],party..skill) then pfm=var["pfm_party_skill_6"] end
	end
	--大内高手
	local check=var["danei_special_1"]
	if check=="大内高手" then
		pfm=var["pfm_danei_1"]
	end
	--大内高手+门派+技能
	local check=var["danei_skills_special_1"]..var["danei_skills_special_2"]..var["danei_skills_special_3"]..var["danei_skills_special_4"]..var["danei_skills_special_5"]..var["danei_skills_special_6"]
	if check~="" then
		if string.find(var["danei_skills_special_1"],super..party..skill) then pfm=var["pfm_danei_skill_1"] end
		if string.find(var["danei_skills_special_2"],super..party..skill) then pfm=var["pfm_danei_skill_2"] end
		if string.find(var["danei_skills_special_3"],super..party..skill) then pfm=var["pfm_danei_skill_3"] end
		if string.find(var["danei_skills_special_4"],super..party..skill) then pfm=var["pfm_danei_skill_4"] end
		if string.find(var["danei_skills_special_5"],super..party..skill) then pfm=var["pfm_danei_skill_5"] end
		if string.find(var["danei_skills_special_6"],super..party..skill) then pfm=var["pfm_danei_skill_6"] end
	end
	if var["bei_special_pfm"] and var["bei_special_pfm"]~="" then
		exec(expand(var["bei_special_pfm"]))
	end
	send("alias pfm "..expand(pfm))
	send("alias pfm_backup "..expand(pfm))
end

-- fight_end 函数: 战斗结束处理
-- 关闭战斗触发器，计算战斗时长，重置战斗相关变量，并取消跟随。
function fight_end()
	close_fight()
--	send("perform hammer.dazhuan")
	var["time_end"]=os.time()
	if var["debug"] and var["debug"]>0 then
--	echo("\n")
--	echo(type(var["time_begin"])..var["time_begin"])
--	echo(type(var["time_end"])..var["time_end"])
	end
	if var["time_begin"]==nil or var["time_end"]==nil then
	var["fight_time"]=100
	else
	var["fight_time"]=var["time_end"]-var["time_begin"]
	end
	
	var["do_stop"]=0
	var["fight_win"]=1
	var["fight"]=0
	var["idle"]=0
	send("follow none")
--	exec(var["skills_bei5"])
	--send("alias pfm "..var["pfm5"])
	send("set wimpycmd hp "..var["char_id"].."\\pfm")
end

-- open_fight 函数: 打开所有战斗相关的触发器
-- 使用function.lua中提供的open_triggers函数批量打开fight_1到fight_46触发器。
function open_fight()
    open_triggers("fight", 1, 46)
end
-- close_fight 函数: 关闭所有战斗相关的触发器
-- 使用function.lua中提供的close_triggers函数批量关闭fight_1到fight_46触发器。
function close_fight()
    close_triggers("fight", 1, 46)
end
close_fight()
Print("--- 加载模块: 战斗 ---")
