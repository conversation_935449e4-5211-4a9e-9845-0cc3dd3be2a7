-- 简单技能放弃脚本
-- 根据设置的技能列表自动放弃技能

-- 设置要放弃的技能列表，用"|"分隔
-- 例如：var["fangqi_skills_list"] = "yuxiao-jian|hamagong|force"
-- var["fangqi_skills_list"] = var["fangqi_skills_list"] or ""  -- 注释掉，让用户完全控制

-- 安全开关：设置为 false 可以完全禁用放弃技能功能
var["fangqi_skills_enabled"] = var["fangqi_skills_enabled"] or true

-- 放弃技能主函数
function fangqi_skills_main()
    -- 安全检查：如果禁用了就直接返回
    if not var["fangqi_skills_enabled"] then
        echo("\n" .. C.r .. "放弃技能功能已禁用")
        return
    end

    local fangqi_list = {}  -- 移到函数内部作为局部变量

    -- 解析放弃技能列表
    local function parse_fangqi_list()
        fangqi_list = {}

        -- 调试信息
        echo("\n" .. C.y .. "调试：var[\"fangqi_skills_list\"] = " .. tostring(var["fangqi_skills_list"]))

        if var["fangqi_skills_list"] and var["fangqi_skills_list"] ~= "" then
            echo("\n" .. C.y .. "调试：开始解析技能列表")
            for skill in string.gmatch(var["fangqi_skills_list"], "([^|]+)") do
                skill = string.gsub(skill, "^%s*(.-)%s*$", "%1")  -- 去除空格
                if skill ~= "" then
                    table.insert(fangqi_list, skill)
                    echo("\n" .. C.y .. "调试：添加技能 " .. skill)
                end
            end
        else
            echo("\n" .. C.g .. "调试：技能列表为空或不存在，不会放弃任何技能")
        end

        echo("\n" .. C.y .. "调试：最终 fangqi_list 长度 = " .. #fangqi_list)
    end

    -- 检查并删除技能
    local function check_and_delete()
        parse_fangqi_list()

        if #fangqi_list == 0 then            
            return
        end        

        -- 使用现有的技能数据
        local skills_name = var["skills_name"] or {}
        local skills_id = var["skills_id"] or {}

        -- 找出需要删除的技能
        local to_delete = {}
        for _, skill in ipairs(fangqi_list) do
            local skill_id = nil

            -- 先检查是否直接是英文ID（在skills_name中存在）
            if skills_name[skill] then
                skill_id = skill
            else
                -- 根据中文名在skills_id中查找英文ID
                skill_id = skills_id[skill]
            end

            if skill_id then
                -- 添加额外验证：检查技能等级是否存在且大于0
                local skills_level = var["skills_level"] or {}
                if skills_level[skill_id] and skills_level[skill_id] > 0 then
                    table.insert(to_delete, skill_id)
                end
            end
        end

        if #to_delete == 0 then            
            return
        end

        -- 只放弃第一个技能
        local skill = to_delete[1]
        local skill_chinese_name = skills_name[skill] or skill  -- 获取中文名，如果没有就用英文ID
        send("fangqi " .. skill)
        send("y")
        log_message("放弃技能：" .. skill_chinese_name, C.G)
    end

    -- 执行检查和删除
    check_and_delete()
end